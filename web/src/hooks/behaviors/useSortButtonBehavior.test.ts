import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { onClickBookmarksSortButtonImpl, onClickResultsSortButtonImpl, onSwitchBookmarksListImpl } from './useSortButtonBehavior';
import mockMatchMedia from '../../mocks/match-media';
import { SearchListMode } from '../../components/domains/split-view/types/SearchListMode';

jest.mock('../../utilities/environment');

// matchMediaのmock
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const matchMediaMock = mockMatchMedia();

describe('useSortButtonBehavior', () => {
  const setBookmarkSortContextsMock = jest.fn();
  const dispatchMock = jest.fn();
  const reportEventMock = jest.fn();
  describe('onClickResultsSortButtonImpl', () => {
    const replaceContextsMock = jest.fn();
    const updateContextFieldApiMock = jest.fn();

    beforeEach(() => {
      replaceContextsMock.mockClear();
      updateContextFieldApiMock.mockClear();
      dispatchMock.mockClear();
      reportEventMock.mockClear();
    });

    // デフォルトモードでのソート動作をテスト
    it('デフォルトモードでdisplayDateキーによるソートが実行される', async () => {
      await expect(onClickResultsSortButtonImpl(
        {
          sort: [{ key: 'displayDate', order: 'asc', priority: 1 }],
          filter: [],
        },
        dispatchMock,
        reportEventMock,
        SearchListMode.DEFAULT,
      )).resolves.toBeUndefined();
      expect(reportEventMock).toBeCalledTimes(1);
      expect(reportEventMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_DEFAULT_SEARCH_LIST_SORT',
      });

      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_SORT',
        payload: {
          sort: [{ key: 'displayDate', order: 'desc', priority: 1 }],
        },
      });
    });

    // チャットモードでのソート動作をテスト
    it('チャットモードでscoreキーによるソートが実行される', async () => {
      await expect(onClickResultsSortButtonImpl(
        {
          sort: [{ key: 'score', order: 'asc', priority: 1 }],
          filter: [],
        },
        dispatchMock,
        reportEventMock,
        SearchListMode.Chat,
      )).resolves.toBeUndefined();
      expect(reportEventMock).toBeCalledTimes(1);
      expect(reportEventMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_CHAT_SEARCH_LIST_SORT',
      });

      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_SORT',
        payload: {
          sort: [{ key: 'score', order: 'desc', priority: 1 }],
        },
      });
    });

    // チャットモードで初回ソート時のテスト
    it('チャットモードで初回ソート時にscoreキーでdescソートが設定される', async () => {
      await expect(onClickResultsSortButtonImpl(
        {
          sort: [],
          filter: [],
        },
        dispatchMock,
        reportEventMock,
        SearchListMode.Chat,
      )).resolves.toBeUndefined();
      expect(reportEventMock).toBeCalledTimes(1);
      expect(reportEventMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_CHAT_SEARCH_LIST_SORT',
      });

      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_SORT',
        payload: {
          sort: [{ key: 'score', order: 'desc', priority: 1 }],
        },
      });
    });

    // デフォルトモードで初回ソート時のテスト
    it('デフォルトモードで初回ソート時にdisplayDateキーでdescソートが設定される', async () => {
      await expect(onClickResultsSortButtonImpl(
        {
          sort: [],
          filter: [],
        },
        dispatchMock,
        reportEventMock,
        SearchListMode.DEFAULT,
      )).resolves.toBeUndefined();
      expect(reportEventMock).toBeCalledTimes(1);
      expect(reportEventMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_DEFAULT_SEARCH_LIST_SORT',
      });

      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_SORT',
        payload: {
          sort: [{ key: 'displayDate', order: 'desc', priority: 1 }],
        },
      });
    });

    // モード切り替え時の異なるキーでのソート動作をテスト
    it('デフォルトモードからチャットモードに切り替わった際にソートキーが変更される', async () => {
      // デフォルトモードでdisplayDateソートが設定されている状態
      await expect(onClickResultsSortButtonImpl(
        {
          sort: [{ key: 'displayDate', order: 'desc', priority: 1 }],
          filter: [],
        },
        dispatchMock,
        reportEventMock,
        SearchListMode.Chat, // チャットモードに切り替え
      )).resolves.toBeUndefined();

      // scoreキーでのソートが実行されることを確認
      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_SORT',
        payload: {
          sort: [
            { key: 'displayDate', order: 'desc', priority: 1 }, // 既存のdisplayDateソートは残る
            { key: 'score', order: 'desc', priority: 1 }, // 新しくscoreソートが追加される
          ],
        },
      });
    });
  });

  describe('onClickBookmarksSortButtonImpl', () => {
    beforeEach(() => {
      setBookmarkSortContextsMock.mockClear();
      dispatchMock.mockClear();
      reportEventMock.mockClear();
    });
    it('outputs a log and execute functions', async () => {
      await expect(onClickBookmarksSortButtonImpl(
        {
          sort: [{ key: 'displayDate', order: 'asc', priority: 1 }],
          filter: [],
        },
        dispatchMock,
        reportEventMock,
      )).resolves.toBeUndefined();
      expect(reportEventMock).toBeCalledTimes(1);
      expect(reportEventMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_BOOKMARK_LIST_SORT',
      });

      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_SORT',
        payload: {
          sort: [{ key: 'displayDate', order: 'desc', priority: 1 }],
        },
      });
    });
  });

  describe('onSwitchBookmarksListImpl', () => {
    beforeEach(() => {
      setBookmarkSortContextsMock.mockClear();
      dispatchMock.mockClear();
    });

    it('should be in default order', async () => {
      expect(onSwitchBookmarksListImpl(
        dispatchMock,
      )).toBeUndefined();
      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_FILTER',
        payload: {
          filter: [],
        },
      });
      expect(dispatchMock).toHaveBeenCalledWith({
        type: 'SET_SORT',
        payload: {
          sort: [],
        },
      });
    });
  });
});
