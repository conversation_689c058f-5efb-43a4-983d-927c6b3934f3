import { EventReporter, EventReportType, MetricReporter } from '@avanade-teams/app-insights-reporter';
import * as React from 'react';
import { GraphError } from '@microsoft/microsoft-graph-client';
import { ISearchWords } from '../../../../../../types/ISearchWords';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../../split-view-detail/SplitViewDetail';
import { SplitViewListMessage, SplitViewListView, SplitViewListViewType } from '../../../split-view-list/SplitViewList';
import { ListMode, ListModeType } from '../../../types/ListMode';
import { SplitViewDispatch } from '../../reducers/splitViewReducer';
import { ISharePointListsResponse } from '../../../../../../types/ISharePointListsResponse';
import { GetSearchRequest<PERSON>pi, PostSearchRequestApi } from '../../../../../../hooks/accessors/useSearchRequestApiAccessor';
import { ISearchRequestResult, ISpecificResult, SearchRequestState } from '../../../../../../types/ISearchRequestResult';
import type { IProcess, IRequestManager } from '../useSearchResultList';
import { ISplitViewListSingle } from '../../../types/ISplitViewListSingle';
import { FetchSPOList } from '../../../../../../hooks/accessors/useSharePointApiAccessor';
import { logPerformanceMetric } from '../../../../../../utilities/commonFunction';
import { isPC } from '../../../../../../utilities/mediaQuery';
import {
  convertChatResponseToSplitViewSingle,
  convertMailResponseToSplitViewListSingle,
  convertSPListSingleToSplitViewListSingle,
  convertSplitViewListSingleToSearchResult,
  extractChatContentPreviewForAI,
  extractSearchKeywordsArray,
} from '../../../../../../utilities/transform';
import useSearchResultRepositoryAccessor, { AddResults, UpdateRequest, UpdateResults } from '../../../../../../hooks/accessors/useSearchResultRepositoryAccessor';
import { ISearchRequestCache, CacheStatus } from '../../../../../../types/IGeraniumAttaneDB';
import { sortArrayByKey } from '../../../../../../utilities/array';
import { IContext, ISortOrder } from '../../../../../../types/IContext';
import SearchRequestError from '../../../../../../utilities/errors/searchRequestError';
import { DataSourceKind } from '../../../../../../types/DataSourceKind';
import { bulkResponseConverter, FetchGroupIds, IBatchResponseStatus } from '../../../../../../hooks/accessors/useGraphApiAccessor';
import { IMailResponse } from '../../../../../../types/IMailResponse';
import { IChatResponse } from '../../../../../../types/IChatResponse';
import environment from '../../../../../../utilities/environment';
import { PerformanceMetrics } from '../../../../../../types/PerformanceMetrics';
import chatItemBatchResponseConverter from '../../../../../../utilities/chatItemBatchResponseConverter';

/**
 * 検索要求APIのStateが処理中のときのインターバル取得タイミング(ミリ秒)
*/
const IN_PROGRESS_INTERVAL = 3 * 1000;

/**
 * 各インターバル取得時間毎のリトライ回数
 */
const RETRY_COUNT = environment.REACT_APP_RETRIABLE_TIME;

const METRIC_NAME = 'useSearchResultList:';

// SPOからリストを取得する型だと思われるが使用されてない
export type FetchSPOListByCaml = (
  baseUrl: string, listGUID: string, searchWords?: ISearchWords,
) => Promise<ISharePointListsResponse>;

// 検索文字を作成する型だと思われるが使用されてない
export type CreateSearchWords = (words: string[]) => ISearchWords;

// キーワードと文字数の上限値
const MAX_KEYWORDS = 100;
const MAX_CHARACTERS = 255;

/**
 * 検索覧の文字列を検索ワードの配列に変換する
 * 半角スペースまたは全角スペースを区切り文字としてみなす
 * @param value
 */
export function splitSearchInput(value: string): string[] {
  const HALF_SPACE = ' ';
  const FULL_SPACE = '　';

  return value
    // FULL_SPACEをHALF_SPACEに置き換え
    .replace(new RegExp(FULL_SPACE, 'g'), HALF_SPACE)
    // 先頭にあるスペースをトリム
    .replace(/^\s/, '')
    // 連続するスペースをトリム
    .replace(/\s+/g, HALF_SPACE)
    // 半角スペースを区切り文字として配列化
    .split(HALF_SPACE)
    // 空文字列のエントリー削除
    .filter((v) => v !== '');
}

/**
 * 入力イベント(onChange)のコールバック実装
 * @param listMode
 * @param listView
 * @param value
 * @param setInputValue
 */
export function onChangeSearchInputImpl(
  listMode: ListModeType,
  listView: SplitViewListViewType,
  value: string,
  setInputValue: (v: string) => void,
): void {
  if (listMode === ListMode.SEARCH) {
    // 検索途中時の値変更は許可しない
    if (listView === SplitViewListView.LOADING) {
      return;
    }
  }
  setInputValue(value);
}

/**
 * 与えられたreqIdでrefを初期化する
 */
export function initializeRefs(
  newReqId: string,
  setAllListDataRef: (values: ISplitViewListSingle[]) => void,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
): void {
  setAllListDataRef([]);
  setRequestManagerRef({ reqId: newReqId, retryCount: 0, processes: [] });
}

/**
 * クリアイベント(onClear)のコールバックの実装
 * @param listMode
 * @param setListMode
 * @param setInputValue
 * @param dispatch
 */
export function onClearSearchInputImpl(
  listMode: ListModeType,
  setListMode: (mode: ListModeType) => void,
  setInputValue: (value: string) => void,
  setSearchWords: (value: string) => void,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  setAllListDataRef: (list: ISplitViewListSingle[]) => void,
  dispatch: SplitViewDispatch,
): void {

  setInputValue('');

  setSearchWords('');

  initializeRefs('', setAllListDataRef, setRequestManagerRef);

  // 初期表示モードに戻す
  setListMode(ListMode.INITIAL_DISPLAY);

  dispatch({
    type: 'SET_DATA',
    payload: {
      listView: SplitViewListView.DEFAULT,
    },
  });
}

/**
 * フォーカスアウトイベント(onBlur)のコールバック実装
 * @param listMode
 * @param inputValue
 * @param setInputValue
 */
export function onBlurSearchInputImpl(
  listMode: ListModeType,
  inputValue: string,
  setInputValue: (value: string) => void,
): void {
  if (listMode !== ListMode.SEARCH) return;
  if (inputValue !== '') return;
  setInputValue(inputValue);
}

/**
 * 送信イベント(onSubmit)のコールバック実装
 * 検索の前段階として、すべての状態をリセットする
 * @return 戻り値がある場合コンテナ側でrefに代入する
 */
export function onSubmitSearchInputImpl(
  setListMode: (mode: ListModeType) => void,
  query: string,
  setSubmittedValue: (v: string) => void,
  setSearchRequest: (result: ISearchRequestResult | null) => void,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  setAllListDataRef: (list: ISplitViewListSingle[]) => void,
  setSyncBookmark: (start: boolean) => void,
  setSearchRequestCache: (request: ISearchRequestCache | undefined) => void,
  setSearchResultsCache: (results: ISplitViewListSingle[]) => void,
  resetCancelRef: () => void,
  dispatch: SplitViewDispatch,
): string {
  const inputs = splitSearchInput(query);
  // 検索ワードが無いときは何もしない
  if (inputs.length === 0) return '';
  // モードを切り替える
  setListMode(ListMode.SEARCH);
  // 生成されたクエリで検索する
  setSubmittedValue(query);
  // お気に入り同期開始
  setSyncBookmark(true);
  // 検索要求をクリアする
  setSearchRequest(null);
  // リクエスト管理の参照をクリアする
  setRequestManagerRef(null);
  // リストデータを空にする（初期化）
  setAllListDataRef([]);
  // キャンセルフラグをリセット
  resetCancelRef();
  // 検索要求のキャッシュをクリアする
  setSearchRequestCache({});
  // 検索結果のキャッシュをクリアする
  setSearchResultsCache([]);
  // UIをリセット
  dispatch({
    type: 'SET_DATA',
    payload: {
      detailView: SplitViewDetailView.ERROR,
      detailMessage: SplitViewDetailMessage.NOT_SELECTED,
      detail: undefined,
      context: {
        sort: [],
        filter: [],
        timestamp: new Date(),
      },
    },
  });
  // ここをinputValueに変えてもconditionは変わらない
  return query;
}

/**
 * AI検索実行時のログを送信する
 * @param resultCount 検索結果件数
 * @param reportEvent ログ送信用のコールバック
 */
export function sendAISearchLog(
  inputValue: string,
  resultCount: number,
  reportEvent: EventReporter,
): void {
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: 'EXECUTE_AI_SEARCH',
    customProperties: {
      searchQuery: inputValue,
      resultCount,
      executionDate: new Date().toISOString(),
    },
  });
}

/**
 * AI検索関数
 * @returns
 */
export async function onSubmitChatSearchImpl(
  inputValue: string,
  oid: string,
  fetchResultFromAI: (
    input: string,
    id: string,
    groupIds: string[],
    dateFilterFrom: string,
    dateFilterTo: string,
    sourceFilter: string) => Promise<any>,
  fetchGroupId: FetchGroupIds | undefined,
  setListMode: (mode: ListModeType) => void,
  setInputValue: (v: string) => void,
  from: string,
  to: string,
  setDateFilter: (dateFilter: string) => void,
  sourceFilterRef: string,
  setSourceFilter: (sourceFilter: string) => void,
  setChatLastSubmittedWords: (v: string) => void,
  setChatListDataRef: (list: ISplitViewListSingle[]) => void,
  dispatch: SplitViewDispatch,
  setReply: (s: string) => void,
  convertResultToSplitViewListSingle: (r: any[]) => ISplitViewListSingle[],
  reportEvent: EventReporter,
): Promise<void> {
  if (!fetchResultFromAI || !fetchGroupId) return;
  // 検索モードへ切り替え
  setListMode(ListMode.SEARCH);
  // Inputを空
  setInputValue('');
  // TODO:SPOが検索対象になければスキップする←そんなに速度変わらないと思われる
  // ユーザーのグループIDを取得。
  const groups = await fetchGroupId();
  // 初期表示のために一旦状態をクリア
  // Loading状態に変更
  dispatch({
    type: 'SET_DATA',
    payload: {
      list: [],
      listView: SplitViewListView.LOADING,
      listMessage: SplitViewListMessage.BLANK,
      activeId: '',
      detailView: SplitViewDetailView.LOADING,
      detailMessage: SplitViewDetailMessage.BLANK,
    },
  });
  // リストデータを空にする（初期化）
  setChatListDataRef([]);
  // 検索処理
  fetchResultFromAI(inputValue, oid, groups, from, to, sourceFilterRef)
    .then((result) => {
      // 前回検索クエリ保持
      setChatLastSubmittedWords(inputValue);
      // TODO:SearchInputで状態を把握するようにする
      // ランダムな0から99,999の間の5桁
      setReply(result.reply);
      const formedList = convertResultToSplitViewListSingle(result.list);
      // 前回検索結果一覧
      setChatListDataRef(formedList);
      // 検索後にフィルターを空文字にリセット
      setDateFilter('');
      setSourceFilter('');
      if (result.list && result.list.length > 0) {
        dispatch({
          type: isPC() ? 'SET_LIST_WITH_DEFAULT' : 'SET_LIST',
          payload: {
            list: formedList,
          },
        });
        dispatch({
          type: 'SET_LISTVIEW',
          payload: {
            listView: SplitViewListView.SEARCH_COMPLETED,
          },
        });
        // AI検索のログを送信
        sendAISearchLog(
          inputValue,
          formedList.length,
          reportEvent,
        );
      } else {
        // 検索結果が0件の場合
        dispatch({
          type: 'SET_NO_ITEMS',
          payload: {
            listMessage: SplitViewListMessage.NO_SEARCH_RESULT,
            detailMessage: SplitViewDetailMessage.NO_ITEMS,
          },
        });
        reportEvent({
          type: EventReportType.SYS_EVENT,
          name: 'NO_AI_SEARCH_RESULT',
        });
      }
    })
    .catch((error: Error) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'SEARCH_ERROR',
        error,
      });
      dispatch({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
    });
}

/**
 * 前回検索結果への切替コールバック実装
 * @param setListMode
 * @param condition
 * @param setSubmittedValue
 * @return 戻り値がある場合コンテナ側でrefに代入する
 */
export function onSwitchLastSearchResultImpl(
  setListMode: (mode: ListModeType) => void,
  condition: string,
  setSubmittedValue: (v: string) => void,
): string {

  // 検索要求から取得した文字列を検索文字列[]として整形済の値
  const inputs = splitSearchInput(condition);
  // 検索ワードが無いときは何もしない
  if (inputs.length === 0) return '';
  // モードを切り替える
  setListMode(ListMode.SEARCH);
  // submittedValueを更新する
  // TODO:見た目の都合で一旦保持しない、次の修正で改善する
  setSubmittedValue(condition);

  return condition;
}

/**
 * 検索実行時のログを送信する
 * @param submittedWords 検索文字列の配列
 * @param reportEvent ログ送信用のコールバック
 */
export function sendSearchLog(
  submittedWords: ISearchWords,
  reportEvent: EventReporter,
): void {
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: 'EXECUTE_SEARCH',
    customProperties: {
      searchWords: JSON.stringify(submittedWords.userInputs.split(/\s+/g)),
      executionDate: new Date().toISOString(),
    },
  });
}

/**
 * APIから検索結果を取得し、SearchRequestへ登録
 */
export async function fetchSearchRequest(
  getSearchRequestApi: GetSearchRequestApi | undefined,
  setSearchRequest: (result: ISearchRequestResult) => void,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  isUnmounted: React.MutableRefObject<boolean>,
): Promise<ISearchRequestResult | void> {

  if (!getSearchRequestApi) return Promise.resolve();

  // 検索結果一覧を取得
  const result = await getSearchRequestApi()
    .catch((reason) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: reason,
      });

      if (isUnmounted.current) return;
      dispatch({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
    });

  if (isUnmounted.current) Promise.resolve();

  if (!result) {
    return Promise.resolve();
  }

  setSearchRequest(result);
  return Promise.resolve(result);
}

export function normalizeSearchWords(searchWord: string) {
  return searchWord.replace(/\s+/g, ' ');
}

/**
 * 検索要求を登録する
 */
export function registerSearchRequest(
  postSearchRequestApi: PostSearchRequestApi | undefined,
  words: ISearchWords,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  onSuccess?: () => void,
): Promise<void> {
  if (!postSearchRequestApi) return Promise.resolve();
  const searchWord = normalizeSearchWords(words.combinedWords);
  return postSearchRequestApi(searchWord)
    .catch((reason) => {
      setRequestManagerRef({ hasError: true });

      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: reason,
      });
      if (reason instanceof SearchRequestError && reason.details['value.condition']) {
        dispatch({
          type: 'SET_ERROR',
          payload: reason.details['value.condition'],
        });
        dispatch({
          type: 'SET_DETAIL_ERROR',
          payload: SplitViewDetailMessage.NOT_SELECTED,
        });
        return;
      }

      if (isUnmounted.current) return;
      dispatch({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
      dispatch({
        type: 'SET_DETAIL_ERROR',
        payload: SplitViewDetailMessage.API_REQUEST_FAIL,
      });
    })
    .then(() => {
      // ログ送信(正常系・異常系共通)
      sendSearchLog(words, reportEvent);
      if (onSuccess) onSuccess();
    });
}

/**
 * フォームの送信イベントが発火したときの処理
 * 検索要求を登録する関数であり、そのプロセスの一環でfetchSearchRequestを呼び出して検索結果を取得。
 * 検索要求が正しく取得されれば、それをリポジトリや状態に保存。
 */
export async function onUpdateLastSubmittedWords(
  lastSubmittedWords: string,
  postSearchRequestApi: PostSearchRequestApi | undefined,
  getSearchRequestApi: GetSearchRequestApi | undefined,
  setSearchRequest: (result: ISearchRequestResult | null) => void,
  useSearchResultRepositoryReturn: ReturnType<typeof useSearchResultRepositoryAccessor>,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  lastSubmittedRef: React.MutableRefObject<string>,
  listModeCache: React.MutableRefObject<string>,
  setLastSubmittedRef: (v: string) => void,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  metrics: [
    performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
    setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  ],
): Promise<void> {

  if (
    isUnmounted.current
    || lastSubmittedWords.length === 0
    || !postSearchRequestApi
    || !getSearchRequestApi
  ) return Promise.resolve(undefined);

  const splittedWords = splitSearchInput(lastSubmittedWords);

  // 検索ワード数が101ワード以上ならバリデーションエラー
  if (splittedWords.length > MAX_KEYWORDS) {
    dispatch({
      type: 'SET_ERROR',
      payload: SplitViewListMessage.TOO_MANY_SEARCH_KEYWORDS,
    });
    return Promise.resolve();
  }

  const wordsTotal = splittedWords
    .map((word) => word.length)
    .reduce((previousValue, currentValue) => previousValue + currentValue);

  // 検索文字数が256文字以上ならバリデーションエラー
  if (wordsTotal > MAX_CHARACTERS) {
    dispatch({
      type: 'SET_ERROR',
      payload: SplitViewListMessage.TOO_MANY_SEARCH_CHARACTERS,
    });
    return Promise.resolve();
  }

  // 検索実行時にキャッシュクリア
  const { updateRequest, clearCache } = useSearchResultRepositoryReturn;
  if (clearCache) await clearCache();

  // listModeが初期表示画面の時(クリア実行後)は処理を中断する
  if (listModeCache.current === ListMode.INITIAL_DISPLAY) return Promise.resolve();

  // exec only when the word changed
  if (lastSubmittedRef.current === lastSubmittedWords) {
    return Promise.resolve(undefined);
  }
  setLastSubmittedRef(lastSubmittedWords);
  // 類語入りの検索キーワード型を生成
  const searchWords: ISearchWords = {
    userInputs: lastSubmittedWords,
    synonyms: { test: [] },
    combinedWords: lastSubmittedWords,
  };

  // ローディング表示を開始
  dispatch({
    type: 'SET_DATA',
    payload: {
      list: [],
      listView: SplitViewListView.LOADING,
      listMessage: SplitViewListMessage.BLANK,
      activeId: '',
      detailView: SplitViewDetailView.LOADING,
      detailMessage: SplitViewDetailMessage.BLANK,
    },
  });

  // APIへの検索要求リクエスト処理開始時間を記録
  const [performanceMetrics, setPerformanceMetrics] = metrics;
  setPerformanceMetrics({
    allSearchCompleted: {
      ...performanceMetrics.current.allSearchCompleted,
      references: {
        ...performanceMetrics.current.allSearchCompleted.references,
        apiIdsRetrievalStartTime: performance.now(),
      },
    },
  });

  // 検索要求を登録
  return registerSearchRequest(
    postSearchRequestApi,
    searchWords,
    dispatch,
    reportEvent,
    isUnmounted,
    setRequestManagerRef,
    async () => {
      const request = await fetchSearchRequest(
        getSearchRequestApi,
        setSearchRequest,
        dispatch,
        reportEvent,
        isUnmounted,
      );

      // 返却された検索要求をリポジトリに保存
      if (updateRequest && request) await updateRequest(request);
    },
  );
}

/**
 * 検索要求から未処理の検索結果を取得してrefを更新する
 */
export function retrieveUnprocessedResults(
  newRequest: boolean,
  results: ISpecificResult[],
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
): ISpecificResult[] {
  const processedPids = new Set(requestManagerRef.current.processes.map((ref) => ref.pid));
  // refを参照して取得済のリストデータを対象から除外する
  const unprocessedResults = requestManagerRef.current.processes.length === 0
    ? results
    : results.filter((result) => !processedPids.has(result.pid));

  // 処理対象refの更新
  const unprocessedList = unprocessedResults.map((value) => ({
    pid: value.pid,
    status: 'InProgress',
  } as IProcess));

  setRequestManagerRef({
    processes: newRequest
      ? unprocessedList
      : [...requestManagerRef.current.processes, ...unprocessedList],
  });

  return unprocessedResults;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function ofType<T>(source: any[], predicate: (item: any) => item is T): T[] {
  return source.filter((item) => predicate(item)) as T[];
}

/**
 * refの状態によって処理を中断すべきか判断する
 */
export function abortInterval(
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  searchRequest: ISearchRequestResult,
): boolean {
  const { reqId: latestReqId, hasError } = requestManagerRef.current;

  // 処理中にエラーが発生している or refとsearchRequestのreqIdが異なっていたら処理を中断する
  if (hasError || latestReqId !== searchRequest.reqId) {
    return true;
  }
  return false;
}

export function handleBatchRequestBadResponse<T>(
  pid: string,
  kind: string,
  result: IBatchResponseStatus<T> | void,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
) {
  if (!result) return;
  if (result.recoverable.length > 0) {
    const { recoverable } = result;
    reportEvent({
      type: EventReportType.SYS_ERROR,
      name: 'BATCH_REQUEST_RETRY_EXCEEDED',
      customProperties: {
        kind,
        ids: recoverable.map((item) => ({
          id: item.id,
          status: item.status,
        })),
        count: recoverable.length,
      },
    });
  }
  if (result.errors.length > 0) {
    const { errors } = result;
    reportEvent({
      type: EventReportType.SYS_ERROR,
      name: 'BATCH_REQUEST_FAILED',
      customProperties: {
        kind,
        ids: errors.map((item) => ({
          id: item.id,
          status: item.status,
        })),
        count: errors.length,
      },
    });
  }
  if (result.tooManyRequests.length > 0) {
    const { tooManyRequests, totalTooManyRequests } = result;
    reportEvent({
      type: EventReportType.SYS_EVENT,
      name: 'BATCH_REQUEST_RETRY_OCCURRED',
      customProperties: {
        kind,
        totalTooManyRequests,
        tooManyRequests,
      },
    });
  }
  if (result.recoverable.length === 0 && result.errors.length === 0) return;
  dispatch({
    type: 'SET_DATA',
    payload: {
      listMessage: SplitViewListMessage.TOO_MANY_RETRY,
    },
  });
  setRequestManagerRef({
    processes: requestManagerRef.current.processes.map(
      (process) => (process.pid === pid
        ? {
          ...process,
          hasRetryError: true,
        }
        : process),
    ) as IProcess[],
  });

}

/**
 * 一覧画面にAPIから取得した増分を反映する
 */
export function dispatchIncrementsToList(
  // MutableRefObjectとはRefが保持している型
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  increments: ISplitViewListSingle[],
  searchRequest: ISearchRequestResult,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  metrics: [
    performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
    setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  ],
  setAllListDataRef: (values: ISplitViewListSingle[]) => void,
  dispatch: SplitViewDispatch,
  reportMetric: MetricReporter,
): void {
  // 取得済一覧データ
  const mergedList = allListDataRef.current.concat(increments);

  // 画面更新前時点で画面表示上限を超えているかどうか
  if (allListDataRef.current.length <= 200) {

    // APIから取得した検索要求がContextを持っている場合は全件取得できるまでLOADINGにする
    if (mergedList.length > 0 && !searchRequest.context?.sort) {
      // 検索結果表示
      dispatch({
        // PCの場合は初期選択させたいのでSET_LIST_WITH_DEFAULTにする
        // SPの場合は初期選択が無いので、SET_LISTにする
        type: isPC() ? 'SET_LIST_WITH_DEFAULT' : 'SET_LIST',
        payload: {
          list: mergedList,
        },
      });
      // インターバル処理中、一件以上値が取れたらViewをON_INTERVALに
      dispatch({
        type: 'SET_LISTVIEW',
        payload: {
          listView: SplitViewListView.ON_INTERVAL,
        },
      });
      dispatch({
        type: 'SET_DETAILVIEW',
        payload: {
          detailView: SplitViewDetailView.DEFAULT,
        },
      });

      const firstResultsRetrieved = allListDataRef.current.length === 0 && increments.length > 0;
      // 新規に検索を開始して初回結果表示までにかかった時間をロギング
      const [performanceMetrics] = metrics;
      if (firstResultsRetrieved && performanceMetrics.current.showFirstSearchResults.start !== 0) {
        logPerformanceMetric(
          reportMetric,
          `${METRIC_NAME}("${searchRequest.condition}",${requestManagerRef.current.reqId})`,
          'ShowFirstSearchResults',
          performanceMetrics.current.showFirstSearchResults.start,
          performanceMetrics.current.showFirstSearchResults.customProperties,
        );
      }
    } else {
      // 一件も取れてない間はViewをLOADINGに設定
      dispatch({
        type: 'SET_DATA',
        payload: {
          listView: SplitViewListView.LOADING,
        },
      });
    }
  } else {
    // 200件以上の時はlistViewにDISPLAY_LIMITを設定
    dispatch({
      type: 'SET_LISTVIEW',
      payload: {
        listView: SplitViewListView.ON_INTERVAL,
        listMessage: SplitViewListMessage.DISPLAY_LIMIT,
      },
    });
  }

  // リストのRefに取得済の一覧を保存
  setAllListDataRef(mergedList);
}

export function setPartialSearchResultCache(
  searchResultsCache: ISplitViewListSingle[],
  cachedPidsRef: React.MutableRefObject<string[]>,
  dispatch: SplitViewDispatch,
  setRequestManagerRef: (override: Partial<IRequestManager>) => void,
  setAllListDataRef: (items: ISplitViewListSingle[]) => void,
  setMetrics: () => void,
) {
  // 検索結果表示
  dispatch({
    // PCの場合は初期選択させたいのでSET_LIST_WITH_DEFAULTにする
    // SPの場合は初期選択が無いので、SET_LISTにする
    type: isPC() ? 'SET_LIST_WITH_DEFAULT' : 'SET_LIST',
    payload: {
      list: searchResultsCache,
    },
  });

  setMetrics();

  // キャッシュ済のpidを処理完了したデータとしてrefに追加する
  setRequestManagerRef({
    processes: cachedPidsRef.current.map(
      (cachedPid) => ({ pid: cachedPid, status: 'Done' }),
    ) as IProcess[],
  });

  if (searchResultsCache.length <= 200) {
    dispatch({
      type: 'SET_LISTVIEW',
      payload: {
        listView: SplitViewListView.ON_INTERVAL,
      },
    });
  } else {
    dispatch({
      type: 'SET_LISTVIEW',
      payload: {
        listView: SplitViewListView.ON_INTERVAL,
        listMessage: SplitViewListMessage.DISPLAY_LIMIT,
      },
    });
  }

  // リストのRefに取得済の一覧を保存
  setAllListDataRef(searchResultsCache);
}

export function checkSameRequestCacheRequestAndAnyResultsCache(
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  searchResultsCache?: ISplitViewListSingle[],
  searchRequestCache?: ISearchRequestCache,
  searchRequest?: ISearchRequestResult | null,
) {
  return searchResultsCache
    && searchRequestCache
    && searchRequest
    && (searchRequestCache.status === CacheStatus.REQUEST_IN_PROGRESS
      || searchRequestCache.status === CacheStatus.SEARCH_ON_INTERVAL)
    && allListDataRef.current.length === 0 && searchResultsCache.length > 0
    && searchRequestCache.result?.reqId === searchRequest.reqId;
}

export function getUniqueIds(
  items: string[] | undefined,
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
) {
  // items が undefined の場合は空配列扱い
  const safeItems = items ?? [];
  // 重複を除去
  const uniqueItems = Array.from(new Set(safeItems));
  // allListDataRef.currentに無いものだけを残す
  const filtered = uniqueItems.filter(
    (id) => !allListDataRef.current.some((result) => result.id === id),
  );
  return filtered;
  // 可読性低いので削除予定
  // itemsはidの配列
  // allListDataRefは空のISplitViewListSingle
  // return (items ?? []).reduce(
  //   (ids, id) => (ids.find((i) => i === id) ? ids : [...ids, id]),
  //   [] as string[],
  // ).filter(
  //   (id) => !allListDataRef.current.some((result) => result.id === id),
  // );
}

/**
 * 対象(pid)毎のリクエストが完了しているかどうか判定する
 */
function isRequestCompleted(
  itemIdsLength: number,
  addItemsLength: number,
  cancellationRef: React.MutableRefObject<boolean>,
): boolean {
  if (cancellationRef.current) return true;
  return itemIdsLength === addItemsLength;
}

/**
 * メールアイテムの検索処理を実行する
 */
async function processMailItems(
  specificResult: ISpecificResult,
  addResults: AddResults | undefined,
  process: (
    results: ISpecificResult,
    appendItems: ((items: (IChatResponse)[]) => void) | ((items: (IMailResponse)[]) => void),
  ) => Promise<IBatchResponseStatus<IMailResponse | IChatResponse>>,
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
  setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  searchRequest: ISearchRequestResult,
): Promise<ISplitViewListSingle[]> {
  const addItems: ISplitViewListSingle[] = [];
  const { pid, ids: itemIds } = specificResult;

  if (!itemIds || !pid) return addItems;

  // 中断すべきか判断する共通関数
  if (abortInterval(requestManagerRef, searchRequest)) return addItems;

  // メールアイテムの検索開始時刻を記録
  const searchStartTime = performance.now();

  // pidをKeyに検索結果キャッシュレコードを追加する
  if (addResults) await addResults(pid, itemIds);

  const appendItems = (items: IMailResponse[]) => {
    addItems.push(...convertMailResponseToSplitViewListSingle(
      items,
      specificResult.dataSource!,
    ));
  };

  const uniqueIds = getUniqueIds(specificResult.ids, allListDataRef);
  const result = await process({ ...specificResult, ids: uniqueIds }, appendItems)
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'SEARCH_MAIL_REJECTS',
        error: reason,
      });

      dispatch({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
    });

  handleBatchRequestBadResponse(
    pid,
    'Mail',
    result,
    requestManagerRef,
    setRequestManagerRef,
    dispatch,
    reportEvent,
  );

  // メール検索にかかった時間の合計と件数を記録する
  setPerformanceMetrics({
    allSearchCompleted: {
      ...performanceMetrics.current.allSearchCompleted,
      customProperties: {
        ...performanceMetrics.current.allSearchCompleted.customProperties,
        mailSearchTimeElapsed:
          performanceMetrics.current.allSearchCompleted.customProperties.mailSearchTimeElapsed
          + (performance.now() - searchStartTime),
        mailSearchItemCount:
          performanceMetrics.current.allSearchCompleted.customProperties.mailSearchItemCount
          + (specificResult.ids?.length ?? 0),
      },
    },
  });

  return addItems;
}

/**
 * SPOアイテムの検索処理を実行する
 */
async function processSPOItems(
  specificResult: ISpecificResult,
  fetchSPOList: FetchSPOList | undefined,
  addResults: AddResults | undefined,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
  setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  searchRequest: ISearchRequestResult,
): Promise<ISplitViewListSingle[]> {
  const addItems: ISplitViewListSingle[] = [];
  const { pid, ids: itemIds, dataSource } = specificResult;
  const baseUrl = dataSource?.properties?.site;
  const listGUID = dataSource?.properties?.list;
  const category = dataSource?.properties?.category;

  if (!itemIds || !pid || !baseUrl || !listGUID) return addItems;

  // 中断すべきか判断する共通関数
  if (abortInterval(requestManagerRef, searchRequest)) return addItems;

  // SPOアイテムの検索開始時刻を記録
  const searchStartTime = performance.now();

  // pidをKeyに検索結果キャッシュレコードを追加する
  if (addResults) await addResults(pid, itemIds);

  if (!fetchSPOList) return addItems;

  const result = await fetchSPOList(baseUrl, listGUID, itemIds, category)
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'SEARCH_SPO_LIST_REJECTS',
        error: reason,
      });

      dispatch({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
    });

  if (result === undefined) return addItems;

  addItems.push(...convertSPListSingleToSplitViewListSingle(
    result.value ?? [],
    dataSource ?? {},
  ));

  // SPO検索にかかった時間の合計と件数を記録する
  setPerformanceMetrics({
    allSearchCompleted: {
      ...performanceMetrics.current.allSearchCompleted,
      customProperties: {
        ...performanceMetrics.current.allSearchCompleted.customProperties,
        spoSearchTimeElapsed:
          performanceMetrics.current.allSearchCompleted.customProperties.spoSearchTimeElapsed
          + (performance.now() - searchStartTime),
        spoSearchItemCount:
          performanceMetrics.current.allSearchCompleted.customProperties.spoSearchItemCount
          + (specificResult.ids?.length ?? 0),
      },
    },
  });

  return addItems;
}

/**
 * チャットアイテムの検索処理を実行する
 */
async function processChatItems(
  specificResult: ISpecificResult,
  addResults: AddResults | undefined,
  process: (
    results: ISpecificResult,
    appendItems: ((items: (IChatResponse)[]) => void) | ((items: (IMailResponse)[]) => void),
    converter?: bulkResponseConverter<IChatResponse> | bulkResponseConverter<IMailResponse>,
  ) => Promise<IBatchResponseStatus<IMailResponse | IChatResponse>>,
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  searchRequest: ISearchRequestResult,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
  setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
): Promise<ISplitViewListSingle[]> {
  const addItems: ISplitViewListSingle[] = [];
  const { pid, ids: itemIds } = specificResult;

  if (!itemIds || !pid) return addItems;

  // 中断すべきか判断する共通関数
  if (abortInterval(requestManagerRef, searchRequest)) return addItems;

  // チャットアイテムの検索開始時刻を記録
  const searchStartTime = performance.now();

  // pidをKeyに検索結果キャッシュレコードを追加する
  if (addResults) await addResults(pid, itemIds);

  const appendItems = (items: IChatResponse[]) => {
    addItems.push(...convertChatResponseToSplitViewSingle(
      items,
      specificResult.dataSource!,
      extractSearchKeywordsArray(searchRequest.conditionKeywords ?? ''),
    ));
  };

  const uniqueIds = getUniqueIds(specificResult.ids, allListDataRef);
  const result = await process(
    { ...specificResult, ids: uniqueIds },
    appendItems,
    chatItemBatchResponseConverter,
  )
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'SEARCH_CHAT_REJECTS',
        error: reason,
      });

      dispatch({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
    });

  handleBatchRequestBadResponse(
    pid,
    'Chat',
    result,
    requestManagerRef,
    setRequestManagerRef,
    dispatch,
    reportEvent,
  );

  // チャット検索にかかった時間の合計と件数を記録する
  setPerformanceMetrics({
    allSearchCompleted: {
      ...performanceMetrics.current.allSearchCompleted,
      customProperties: {
        ...performanceMetrics.current.allSearchCompleted.customProperties,
        chatSearchTimeElapsed:
          performanceMetrics.current.allSearchCompleted.customProperties.chatSearchTimeElapsed
          + (performance.now() - searchStartTime),
        chatSearchItemCount:
          performanceMetrics.current.allSearchCompleted.customProperties.chatSearchItemCount
          + (specificResult.ids?.length ?? 0),
      },
    },
  });

  return addItems;
}

/**
 * 検索要求から未処理の検索結果を取得してrefを更新する
 */
export function fetchRetrievedListItems(
  searchRequest: ISearchRequestResult | null,
  fetchSPOList: FetchSPOList | undefined,
  addResults: AddResults | undefined,
  updateResults: UpdateResults | undefined,
  process: (
    results: ISpecificResult,
    appendItems: ((items: (IChatResponse)[]) => void) | ((items: (IMailResponse)[]) => void),
  ) => Promise<IBatchResponseStatus<IMailResponse | IChatResponse>>,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  reportMetric: MetricReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  metrics: [
    performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
    setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  ],
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  cancellationRef: React.MutableRefObject<boolean>,
  setAllListDataRef: (values: ISplitViewListSingle[]) => void,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  onSuccess?: () => void,
): Promise<void> {
  if (
    !fetchSPOList
    || !searchRequest
    || !searchRequest.results
    || !searchRequest.reqId) {
    if (onSuccess) onSuccess();
    return Promise.resolve();
  }

  if (abortInterval(requestManagerRef, searchRequest)) return Promise.resolve();

  const isNewRequest = requestManagerRef.current.processes.length === 0;

  const unprocessedResults = retrieveUnprocessedResults(
    isNewRequest,
    searchRequest.results,
    requestManagerRef,
    setRequestManagerRef,
  );

  // 未処理の検索結果が存在しない場合は処理をSkipする
  if (unprocessedResults.length === 0) {
    // stateがinProgressの場合はインターバル処理を続行
    if (onSuccess) onSuccess();
    return Promise.resolve();
  }

  const [performanceMetrics, setPerformanceMetrics] = metrics;

  unprocessedResults.forEach(async (specificResult) => {
    const { pid, ids: itemIds } = specificResult;

    if (!itemIds || !pid || !searchRequest.reqId) return Promise.resolve();

    let addItems: ISplitViewListSingle[] = [];

    if (specificResult.dataSource?.kind === DataSourceKind.Mail) {
      addItems = await processMailItems(
        specificResult,
        addResults,
        process,
        allListDataRef,
        dispatch,
        reportEvent,
        performanceMetrics,
        setPerformanceMetrics,
        requestManagerRef,
        setRequestManagerRef,
        searchRequest,
      );
    } else if (specificResult.dataSource?.kind === DataSourceKind.SPO) {
      addItems = await processSPOItems(
        specificResult,
        fetchSPOList,
        addResults,
        dispatch,
        reportEvent,
        performanceMetrics,
        setPerformanceMetrics,
        requestManagerRef,
        setRequestManagerRef,
        searchRequest,
      );
    } else if (specificResult.dataSource?.kind === 'Chat') {
      addItems = await processChatItems(
        specificResult,
        addResults,
        process,
        allListDataRef,
        searchRequest,
        dispatch,
        reportEvent,
        performanceMetrics,
        setPerformanceMetrics,
        requestManagerRef,
        setRequestManagerRef,
      );
    }

    if (abortInterval(requestManagerRef, searchRequest)) return Promise.resolve();

    const completed = isRequestCompleted(itemIds.length, addItems.length, cancellationRef);
    setRequestManagerRef({
      hasIncompleteRequests: requestManagerRef.current.hasIncompleteRequests || !completed,
    });
    // 検索結果キャッシュレコードを更新する
    if (updateResults && completed) {
      updateResults(
        pid ?? '',
        addItems.length === 0
          ? []
          : convertSplitViewListSingleToSearchResult(addItems),
      );
    }

    if (!cancellationRef.current) {
      dispatchIncrementsToList(
        allListDataRef,
        addItems,
        searchRequest,
        requestManagerRef,
        metrics,
        setAllListDataRef,
        dispatch,
        reportMetric,
      );
    }

    // 処理完了したrefのStatusを更新する
    setRequestManagerRef({
      processes: requestManagerRef.current.processes.map(
        (value) => (value.pid === pid
          ? { ...value, status: 'Done' }
          : value),
      ) as IProcess[],
    });

    if (onSuccess) onSuccess();

    return Promise.resolve();
  });

  return Promise.resolve();
}

export function sortListByDisplayDate(
  list: ISplitViewListSingle[],
  sortContexts: ISortOrder[] | undefined,
): ISplitViewListSingle[] {
  const current = sortContexts?.find((sort) => sort.key === 'displayDate');
  return current
    ? sortArrayByKey(list, current.key, current.order)
    : list;
}

export function isCacheStatusCompleted(
  searchRequestCache: ISearchRequestCache | undefined,
  searchResultsCache: ISplitViewListSingle[],
  reqId: string,
): boolean {
  if (
    searchResultsCache
    && searchRequestCache
    && searchRequestCache.status === CacheStatus.COMPLETED
    && searchRequestCache.result?.reqId === reqId
  ) return true;

  return false;
}

export function indicateTooManyRetry(
  reportEvent: EventReporter,
  dispatch: SplitViewDispatch,
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
) {
  reportEvent({
    type: EventReportType.SYS_ERROR,
    name: 'TOO_MANY_RETRY',
  });
  if (allListDataRef.current.length > 0) {
    dispatch({
      type: 'SET_DATA',
      payload: {
        listView: SplitViewListView.SEARCH_COMPLETED,
        listMessage: SplitViewListMessage.TOO_MANY_RETRY,
      },
    });
  } else {
    dispatch({
      type: 'SET_DATA',
      payload: {
        listView: SplitViewListView.SEARCH_COMPLETED,
        listMessage: SplitViewListMessage.TOO_MANY_RETRY,
        detailView: SplitViewDetailView.ERROR,
        detailMessage: SplitViewDetailMessage.NO_ITEMS,
      },
    });
  }
}

export function indicateSearchStateError(
  reportEvent: EventReporter,
  dispatch: SplitViewDispatch,
) {
  reportEvent({
    type: EventReportType.SYS_ERROR,
    name: 'SEARCH_STATE_ERROR',
  });
  dispatch({
    type: 'SET_ERROR',
    payload: SplitViewListMessage.API_REQUEST_FAIL,
  });
}

/**
 * 検索結果をキャッシュデータを基に処理し、画面に表示するためのデータ更新を行う
 * @param searchResultsCache
 * @param cacheContext
 * @param allListDataRef
 * @param setAllListDataRef
 * @param dispatch
 * @param setInterval
 * @param performanceMetrics
 * @param setPerformanceMetrics
 */
export function setAllSearchResultCache(
  searchResultsCache: ISplitViewListSingle[],
  cacheContext: IContext | undefined,
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  setAllListDataRef: (values: ISplitViewListSingle[]) => void,
  dispatch: SplitViewDispatch,
  setInterval: React.Dispatch<React.SetStateAction<number | null>>,
  performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
  setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
) {
  // TODO: 型の異なる古いキャッシュデータに対するフォールバック関数を実装して呼び出す
  const converted = searchResultsCache.map((cache) => ({
    ...cache,
    note: cache?.note,
  }));

  // キャッシュされているContextを使って画面表示用リストデータを作成する
  const sortContexts = cacheContext?.sort;
  const sorted = sortListByDisplayDate(converted, sortContexts);
  const extractFunc = (ids: string[]) => {
    const filtered = [];
    // eslint-disable-next-line no-restricted-syntax
    for (const item of sorted) {
      if (ids.includes(item.id)) {
        filtered.push(item);
        if (filtered.length > 200) break;
      }
    }
    return filtered;
  };
  const displayIds = cacheContext?.displayIds;
  const filtered = displayIds ? extractFunc(displayIds) : sorted;

  // 検索結果表示
  dispatch({
    // PCの場合は初期選択させたいのでSET_LIST_WITH_DEFAULTにする
    // SPの場合は初期選択が無いので、SET_LISTにする
    type: isPC() ? 'SET_LIST_WITH_DEFAULT' : 'SET_LIST',
    payload: {
      list: filtered,
    },
  });

  if (filtered.length === 0) {
    dispatch({
      type: 'SET_NO_ITEMS',
      payload: {
        listMessage: SplitViewListMessage.NO_SEARCH_RESULT,
        detailMessage: SplitViewDetailMessage.NO_ITEMS,
      },
    });
  }

  // 前回キャッシュ表示完了時からさらにContextが変わっている場合はactiveIdをリセットする
  const oldId = allListDataRef.current[0]?.id ?? '';
  const newId = filtered[0]?.id ?? '';
  if (isPC() && oldId && newId
    && oldId !== newId) {
    dispatch({
      type: 'SET_ACTIVE',
      payload: {
        activeId: newId,
        title: filtered[0].title ?? '',
        kind: filtered[0].kind ?? 'Other',
      },
    });
  }

  // キャッシュにより一覧が表示されるまでにかかった時間をメトリックのプロパティに設定する
  setPerformanceMetrics({
    viewDetailsFromCache: {
      ...performanceMetrics.current.viewDetailsFromCache,
      customProperties: {
        ...performanceMetrics.current.viewDetailsFromCache.customProperties,
        cacheListDisplayTimeElapsed:
          performance.now() - performanceMetrics.current.viewDetailsFromCache.start,
      },
    },
  });
  // 詳細取得前処理の開始時刻を記録
  setPerformanceMetrics({
    viewDetailsFromCache: {
      ...performanceMetrics.current.viewDetailsFromCache,
      references: {
        ...performanceMetrics.current.viewDetailsFromCache.references,
        apiDetailPreRequestStartTime: performance.now(),
      },
    },
  });

  dispatch({
    type: 'SET_LISTVIEW',
    payload: {
      listView: SplitViewListView.SEARCH_COMPLETED,
    },
  });

  // 検索一覧を更新(全データを持つ必要があるためfilteredではなくsortedを渡す)
  setAllListDataRef(sorted);

  setInterval(null);
}

export function isSearchRequestFulfilled(
  searchRequest: ISearchRequestResult | undefined,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
) {
  return searchRequest
    && searchRequest.results
    && (searchRequest.state === SearchRequestState.COMPLETED
      || searchRequest.state === SearchRequestState.CANCELLED)
    && searchRequest.results.length === requestManagerRef.current.processes.filter((process: { status: string; }) => process.status === 'Done').length;
}

export async function completionIntervel(
  searchRequest: ISearchRequestResult,
  sortContexts: ISortOrder[] | undefined,
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
  setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  dispatch: SplitViewDispatch,
  updateRequest: UpdateRequest | undefined,
  reportEvent: EventReporter,
) {
  // インターバル終了後、リストのデータが0件だったら0件表示
  if (allListDataRef.current.length === 0) {
    dispatch({
      type: 'SET_NO_ITEMS',
      payload: {
        listMessage: SplitViewListMessage.NO_SEARCH_RESULT,
        detailMessage: SplitViewDetailMessage.NO_ITEMS,
      },
    });
    reportEvent({
      type: EventReportType.SYS_EVENT,
      name: 'NO_SEARCH_RESULT',
    });

  } else {
    // APIから取得した検索要求がContextを持っている場合は全件取得できてから結果を表示する
    if (searchRequest && searchRequest.context?.sort) {
      const sortedList = sortListByDisplayDate(allListDataRef.current, sortContexts);
      // 検索結果表示
      dispatch({
        // PCの場合は初期選択させたいのでSET_LIST_WITH_DEFAULTにする
        // SPの場合は初期選択が無いので、SET_LISTにする
        type: isPC() ? 'SET_LIST_WITH_DEFAULT' : 'SET_LIST',
        payload: {
          list: sortedList,
        },
      });
    }

    // インターバル終了後、値が取れていたらViewをSEARCH_COMPLETEDに
    dispatch({
      type: 'SET_LISTVIEW',
      payload: {
        listView: SplitViewListView.SEARCH_COMPLETED,
      },
    });

    // 検索処理の中にリトライ上限/不可のアイテムが存在した場合はメッセージを表示する
    if (requestManagerRef.current.processes.some((process) => process.hasRetryError)) {
      dispatch({
        type: 'SET_DATA',
        payload: {
          listMessage: SplitViewListMessage.TOO_MANY_RETRY,
        },
      });
    }

    if (allListDataRef.current.length > 200) {
      reportEvent({
        type: EventReportType.SYS_EVENT,
        name: 'TOO_MANY_SEARCH_RESULT',
      });
    }
  }

  // キャッシュのStstusを完了にする
  // TODO: 不完全なキャッシュ（バッチリクエストで全部取得できていない）がある時はCacheStatus.COMPLETEDを渡さないようにする
  // TODO: 検索は完了しているが、CacheStatus.COMPLETEDにはしないが丸い？CacheStatus.SEARCH_ON_INTERVAL
  // 検索結果の塊がうまく取れない！
  // TODO: RequestManagerRef をなんとかしてエラーあるかどうかをみる
  const requestStatus = requestManagerRef.current.hasIncompleteRequests
    ? CacheStatus.REQUEST_IN_PROGRESS : CacheStatus.COMPLETED;
  if (updateRequest) await updateRequest(searchRequest, requestStatus);

  // インターバル処理開始から終了までにかかった時間をメトリックのプロパティに設定する
  setPerformanceMetrics({
    allSearchCompleted: {
      ...performanceMetrics.current.allSearchCompleted,
      customProperties: {
        ...performanceMetrics.current.allSearchCompleted.customProperties,
        intervalExecutionTimeElapsed:
          performance.now()
          - performanceMetrics.current.allSearchCompleted.references.intervalExecutionStartTime,
      },
    },
  });
}

/**
 * ユーザーによる検索中断のログを送信する
 */
function sendCancellationLog(
  searchRequest: ISearchRequestResult,
  performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
  reportEvent: EventReporter,
): void {
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: 'CLICK_CANCEL',
    customProperties: {
      searchWords: searchRequest.condition ?? '',
      elapsedFromStart: performanceMetrics.current
        .cancellation - performanceMetrics.current.allSearchCompleted.start,
    },
  });
}
/**
 * Mailの場合の変換
 */
export function convertMail(result: any): ISplitViewListSingle {
  const { document } = result;
  return {
    id: document.id ?? '',
    kind: 'Mail',
    score: result.semanticSearch?.rerankerScore,
    title: document.subject ?? 'No Title',
    note: document.from?.emailAddress?.name ?? document.from?.emailAddress?.address,
    displayDate: document.receivedDateTime ?? '表示日時',
    properties: {
      hasAttachments: document.hasAttachments,
    },
  };
}

/**
 * Chatの場合の変換
 */
export function convertChat(result: any): ISplitViewListSingle {
  const { document } = result;
  // messageTypeの初期値としてdocument.messageTypeを使用
  let { messageType } = document;
  // channelIdentityが存在する場合の判別
  if (document.channelIdentity?.teamId && document.channelIdentity?.channelId && document.id) {
    // チームチャネルの場合
    messageType = 'team';
  } else if (document.chatId && document.id) {
    // 1:1チャットの場合
    messageType = 'chat';
  }

  return {
    id: document.id ?? '',
    kind: 'Chat',
    title:
      document.from.user?.displayName
      ?? document.from.application?.displayName
      ?? '',
    note: extractChatContentPreviewForAI(document.chunk),
    displayDate: document.createdDateTime ?? '表示日時',
    properties: {
      hasAttachments: document.hasAttachments,
      chatId: document.chatId ?? '',
      messageType,
    },
  };
}

/**
 * SPOの場合の変換
 */
export function convertSPO(result: any): ISplitViewListSingle {
  return {
    id: result.document.id ?? '',
    kind: 'SPO',
    score: result.semanticSearch?.rerankerScore,
    title: result.document.properties.title ?? 'No Title',
    note: result.document.properties.category1 ?? 'No Category',
    displayDate: result.document.properties.updatedDate ?? '表示日時',
    properties: {
      hasAttachments: result.document.properties.hasAttachments,
      siteUrl: result.document.properties.siteUrl,
      listUrl: result.document.properties.listUrl,
      editLink: result.document.properties.editLink ?? '',
      // 現在のIndexにはない
      listName: result.document.properties.listName ?? 'No Category',
    },
  };
}

/**
 * 上記以外または不明なkindの場合の変換
 */
export function convertUnknown(result: any): ISplitViewListSingle {
  return {
    id: result.id ?? '',
    kind: result.document?.kind ?? 'Unknown',
    title: 'Unknown Title',
    note: 'Unknown Note',
    displayDate: 'Unknown Date',
    properties: {},
  };
}

// 関数が長すぎます。依存関係を整理して、分割を検討してください。
// 途中でガードを書いてあるあたりで分割するのが目安です。
/**
 * インターバル取得のコールバック実装
 */
export async function onIntervalImpl(
  searchRequest: ISearchRequestResult | null,
  getSearchRequestApi: GetSearchRequestApi | undefined,
  fetchList: FetchSPOList | undefined,
  process: (
    results: ISpecificResult,
    appendItems: ((items: (IChatResponse)[]) => void) | ((items: (IMailResponse)[]) => void),
  ) => Promise<IBatchResponseStatus<IMailResponse | IChatResponse>>,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  reportMetric: MetricReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  metrics: [
    performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
    setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  ],
  allListDataRef: React.MutableRefObject<ISplitViewListSingle[]>,
  setAllListDataRef: (values: ISplitViewListSingle[]) => void,
  requestManagerRef: React.MutableRefObject<IRequestManager>,
  setRequestManagerRef: (override: Partial<IRequestManager> | null) => void,
  setSearchRequest: (result: ISearchRequestResult | null) => void,
  setInterval: React.Dispatch<React.SetStateAction<number | null>>,
  useSearchResultRepositoryReturn: ReturnType<typeof useSearchResultRepositoryAccessor>,
  cancellationRef: React.MutableRefObject<boolean>,
  onSuccess?: () => void,
) {
  if (!searchRequest || !searchRequest.results || !searchRequest.reqId) return;

  const {
    updateRequest,
    addResults,
    updateResults,
    searchRequestCache,
    searchResultsCache,
    cachedPidsRef,
    cacheContext,
  } = useSearchResultRepositoryReturn;

  const [performanceMetrics, setPerformanceMetrics] = metrics;

  // reqIdが空の場合新規検索のためrefに初期値を設定する
  if (!requestManagerRef.current.reqId) {
    // 検索一覧を初期化
    initializeRefs(searchRequest.reqId, setAllListDataRef, setRequestManagerRef);
    // 新規検索開始からインターバル処理開始までにかかった時間をメトリックのプロパティに設定する
    setPerformanceMetrics({
      allSearchCompleted: {
        ...performanceMetrics.current.allSearchCompleted,
        customProperties: {
          ...performanceMetrics.current.allSearchCompleted.customProperties,
          intervalProcessingTimeElapsed:
            performance.now() - performanceMetrics.current.allSearchCompleted.start,
        },
      },
    });

    // インターバル処理開始時刻を記録
    setPerformanceMetrics({
      allSearchCompleted: {
        ...performanceMetrics.current.allSearchCompleted,
        references: {
          ...performanceMetrics.current.allSearchCompleted.references,
          intervalExecutionStartTime: performance.now(),
        },
      },
    });
  }

  if (abortInterval(requestManagerRef, searchRequest)) return;

  // TODO: 1回しか呼ばなくていいのに2回呼ばれてるのでガードをかける
  // キャッシュが全件取れていた場合は、結果をdispatchしてインターバル処理を終了
  if (
    isCacheStatusCompleted(
      searchRequestCache, searchResultsCache, searchRequest.reqId,
    )
  ) {
    setAllSearchResultCache(
      searchResultsCache,
      cacheContext,
      allListDataRef,
      setAllListDataRef,
      dispatch,
      setInterval,
      performanceMetrics,
      setPerformanceMetrics,
    );
    return;
  }
  function setMetrics() {
    // キャッシュにより一覧が表示されるまでにかかった時間をメトリックのプロパティに設定する
    setPerformanceMetrics({
      viewDetailsFromCache: {
        ...performanceMetrics.current.viewDetailsFromCache,
        customProperties: {
          ...performanceMetrics.current.viewDetailsFromCache.customProperties,
          cacheListDisplayTimeElapsed:
            performance.now() - performanceMetrics.current.viewDetailsFromCache.start,
        },
      },
    });
    // 詳細取得前処理の開始時刻を記録
    setPerformanceMetrics({
      viewDetailsFromCache: {
        ...performanceMetrics.current.viewDetailsFromCache,
        references: {
          ...performanceMetrics.current.viewDetailsFromCache.references,
          apiDetailPreRequestStartTime: performance.now(),
        },
      },
    });
  }

  // キャッシュに存在するデータを処理済みとして画面に表示して処理を続行する
  const hasPartialCache = checkSameRequestCacheRequestAndAnyResultsCache(
    allListDataRef,
    searchResultsCache,
    searchRequestCache,
    searchRequest,
  );

  if (hasPartialCache) {
    setPartialSearchResultCache(searchResultsCache,
      cachedPidsRef,
      dispatch,
      setRequestManagerRef,
      setAllListDataRef,
      setMetrics);
  }

  // 初回検索結果を取得できたときにリトライ回数をリセットしインターバルの秒数を更新する
  if (searchRequest.results.length > 0 && requestManagerRef.current.processes.length === 0) {
    setRequestManagerRef({ retryCount: 0 });
    setInterval(IN_PROGRESS_INTERVAL);
  }

  // refのpidがすべてDoneになる前にインターバル処理のリトライが規定回数を超えた時検索処理を終了しメッセージを表示する
  if (RETRY_COUNT > 0 && requestManagerRef.current.retryCount > RETRY_COUNT) {
    setRequestManagerRef({ hasError: true });
    indicateTooManyRetry(reportEvent, dispatch, allListDataRef);
    // キャッシュのStatusにエラーをセットする
    if (updateRequest) await updateRequest(searchRequest, CacheStatus.ERROR);
    setInterval(null);
  }

  // 検索要求のStateがErrorの場合はインターバル取得を終了する
  if (searchRequest.state === SearchRequestState.ERROR) {
    setRequestManagerRef({ hasError: true });

    indicateSearchStateError(reportEvent, dispatch);

    // キャッシュのStstusにエラーをセットする
    if (updateRequest) await updateRequest(searchRequest, CacheStatus.ERROR);
    setInterval(null);
    return;
  }

  // 処理待ちリストにinProgressがあれば完了するまで追加取得しない
  if (requestManagerRef.current.processes.some((value) => value.status === 'InProgress')) return;

  // 検索要求のStateがCompletedかつ、対象データが全て取得できている場合はインターバル取得を終了する
  // 以下でrequestManagerRefの中身が全部'Done'になってることを確認してるので、リトライ中に検索終わったり何か変な挙動になったらここを疑う
  // 不完全なpidをDoneにしなければ、ここには入ってこない=CacheStatus.COMPLETEDにならない
  if (isSearchRequestFulfilled(searchRequest, requestManagerRef)) {
    if (cancellationRef.current) {
      sendCancellationLog(searchRequest, performanceMetrics, reportEvent);
    }

    completionIntervel(
      searchRequest,
      cacheContext?.sort,
      allListDataRef,
      requestManagerRef,
      performanceMetrics,
      setPerformanceMetrics,
      dispatch,
      updateRequest,
      reportEvent,
    );

    // 新規検索開始からすべての検索処理が完了して画面に表示されるまでの時間をログ出力する
    logPerformanceMetric(
      reportMetric,
      `${METRIC_NAME}("${searchRequest.condition}",${requestManagerRef.current.reqId})`,
      'AllSearchCompleted',
      performanceMetrics.current.allSearchCompleted.start,
      performanceMetrics.current.allSearchCompleted.customProperties,
    );
    setInterval(null);
    return;
  }

  setRequestManagerRef({ retryCount: requestManagerRef.current.retryCount + 1 });

  if (searchRequest.state === SearchRequestState.IN_PROGRESS) {
    // 新しい検索要求を元にデータを取得しに行く、検索結果はこれ
    const newSearchRequest = await fetchSearchRequest(
      getSearchRequestApi,
      setSearchRequest,
      dispatch,
      reportEvent,
      isUnmounted,
    );
    if (!newSearchRequest) return;

    if (newSearchRequest.state === SearchRequestState.COMPLETED) {
      // API検索要求取得開始から全Idが返ってくるまでにかかった時間
      setPerformanceMetrics({
        allSearchCompleted: {
          ...performanceMetrics.current.allSearchCompleted,
          customProperties: {
            ...performanceMetrics.current.allSearchCompleted.customProperties,
            apiIdsRetrievalTimeElapsed:
              performance.now()
              - performanceMetrics.current.allSearchCompleted.references.apiIdsRetrievalStartTime,
          },
        },
      });
    }

    if (abortInterval(requestManagerRef, searchRequest)) return;

    // 検索要求キャッシュを更新
    if (updateRequest) await updateRequest(newSearchRequest);

    fetchRetrievedListItems(
      newSearchRequest,
      fetchList,
      addResults,
      updateResults,
      process,
      dispatch,
      reportEvent,
      reportMetric,
      isUnmounted,
      metrics,
      allListDataRef,
      cancellationRef,
      setAllListDataRef,
      requestManagerRef,
      setRequestManagerRef,
      onSuccess,
    );

    return;
  }

  // 検索要求キャッシュを更新
  if (updateRequest) await updateRequest(searchRequest);

  // StateがCompletedのときは渡された検索要求からリストデータを取得する
  fetchRetrievedListItems(
    searchRequest,
    fetchList,
    addResults,
    updateResults,
    process,
    dispatch,
    reportEvent,
    reportMetric,
    isUnmounted,
    metrics,
    allListDataRef,
    cancellationRef,
    setAllListDataRef,
    requestManagerRef,
    setRequestManagerRef,
    onSuccess,
  );
}
