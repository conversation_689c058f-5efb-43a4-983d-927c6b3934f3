import * as React from 'react';
import '@testing-library/jest-dom';
import { screen, render, waitFor } from '@testing-library/react';
import { Simulate } from 'react-dom/test-utils';
import mockMatchMedia from '../../../../mocks/match-media';
import { queryElem } from '../../../../utilities/test';
import SearchInput, { Message } from './SearchInput';
import { SearchListMode } from '../../../domains/split-view/types/SearchListMode';

// mock matchMedia
mockMatchMedia();

beforeAll(() => {
  window.HTMLElement.prototype.scrollIntoView = jest.fn();
});

describe('SearchInputコンポーネント', () => {
  const onChangeMock = jest.fn();
  const onSubmitMock = jest.fn();
  const currentValueMock = jest.fn();
  const onAddMessageMock = jest.fn();

  type TestComponentProps = {
    initialValue: string;
    disabled: boolean;
    searchMode?: typeof SearchListMode[keyof typeof SearchListMode];
    messages?: Message[];
  };

  const TestComponent: React.FC<TestComponentProps> = (props) => {
    const {
      initialValue, disabled, searchMode, messages,
    } = props;
    const [value, setValue] = React.useState(initialValue);

    const onChange = React.useCallback((v: string, e: React.SyntheticEvent<HTMLElement>) => {
      setValue(v);
      onChangeMock(v, e);
    }, []);

    React.useEffect(() => {
      currentValueMock(value);
    }, [value]);

    return (
      <SearchInput
        value={value}
        onChange={onChange}
        onSubmit={onSubmitMock}
        disabled={disabled}
        searchMode={searchMode}
        messages={messages}
        onAddMessage={onAddMessageMock}
      />
    );
  };

  function renderComponent(
    initialValue: string,
    summary?: string,
    messages?: Message[],
    onAddMessage?: (msg: Message) => void,
    disabled = false,
    searchMode = SearchListMode.DEFAULT,
  ) {
    return render(
      <TestComponent
        initialValue={initialValue}
        searchMode={searchMode}
        disabled={disabled}
        messages={messages}
        {...(onAddMessage ? { onAddMessage } : {})}
      />,
    );
  }

  beforeEach(() => {
    onChangeMock.mockClear();
    onSubmitMock.mockClear();
    currentValueMock.mockClear();
  });

  describe('デフォルト検索モード', () => {
    describe('入力フィールドに値がない場合', () => {
      it('プレースホルダーテキストに「キーワードを入力」が表示される', () => {
        renderComponent('', SearchListMode.DEFAULT);
        expect(screen.getByPlaceholderText('キーワードを入力')).toBeInTheDocument();
      });

      it('検索アイコンが表示される', () => {
        const { container } = renderComponent('', SearchListMode.DEFAULT);
        expect(container).toContainHTML('search-input-find-icon');
      });

      describe('入力フィールドがフォーカスされた場合', () => {
        it('プレースホルダーテキストに「タイトル、分類、本文を検索」が表示される', async () => {
          const { container } = renderComponent('', SearchListMode.DEFAULT);
          Simulate.focus(queryElem(container, 'input'));
          await expect(screen.findByPlaceholderText('タイトル、分類、本文を検索')).resolves.toBeInTheDocument();
        });
      });
    });
  });

  describe('入力フィールドに値がある場合', () => {
    it('入力値が表示される', () => {
      renderComponent('abcd');
      expect(screen.getByDisplayValue('abcd')).toBeInTheDocument();
    });

    it('検索アイコンが表示されない', () => {
      const { container } = renderComponent('abcd');
      expect(container).not.toHaveClass('search-input-find-icon');
    });

    describe('入力フィールドがフォーカスされた場合', () => {
      it('入力値が表示される', () => {
        const { container } = renderComponent('abcd');
        Simulate.focus(queryElem(container, 'input'));
        expect(screen.getByDisplayValue('abcd')).toBeInTheDocument();
      });

      it('検索アイコンが表示されない', async () => {
        const { container } = renderComponent('abcd');
        Simulate.focus(queryElem(container, 'input'));
        await waitFor(async () => expect(container).not.toContainHTML('search-input-find-icon'));
      });
    });
  });

  describe('無効化状態', () => {
    describe('disabledがfalseの場合', () => {
      it('入力フィールドが無効化されない', () => {
        const { container } = renderComponent('', '', undefined, undefined, false);
        expect(queryElem(container, 'input')).not.toBeDisabled();
      });
    });

    describe('disabledがtrueの場合', () => {
      it('入力フィールドが無効化される', () => {
        const { container } = renderComponent('', '', undefined, undefined, true);
        expect(queryElem(container, 'input')).toBeDisabled();
      });
    });
  });

  describe('AI検索モード（チャットモード）', () => {
    describe('searchModeがChatの場合', () => {
      // AI検索モードでのプレースホルダーテキストのテスト
      it('入力値がない場合、プレースホルダーテキストに「自然言語で検索できます」が表示される', () => {
        renderComponent('', '', undefined, undefined, false, SearchListMode.Chat);
        expect(screen.getByPlaceholderText('自然言語で検索できます')).toBeInTheDocument();
      });

      // AI検索モードでフォーカス時のプレースホルダーテスト
      it('入力フィールドがフォーカスされた場合、プレースホルダーテキストが空になる', async () => {
        const { container } = renderComponent('', '', undefined, undefined, false, SearchListMode.Chat);
        Simulate.focus(queryElem(container, 'input'));
        await waitFor(() => {
          const input = queryElem(container, 'input');
          expect(input.getAttribute('placeholder')).toBe('');
        });
      });

      // チャットコンテナの表示テスト
      it('チャットコンテナが表示される', () => {
        const { container } = renderComponent('', '', undefined, undefined, false, SearchListMode.Chat);
        expect(container.querySelector('.chat-container')).toBeInTheDocument();
      });

      // 初期メッセージの表示テスト
      it('atTaneからの初期メッセージが表示される', () => {
        renderComponent('', '', undefined, undefined, false, SearchListMode.Chat);
        expect(screen.getByText('お手伝いできることはありますか？')).toBeInTheDocument();
      });
    });

    describe('チャットモードでの送信時', () => {
      // チャットモードでの送信時にメッセージが追加されることをテスト
      it('送信時にユーザーメッセージがonAddMessageでコールバックされる', async () => {
        const { container } = renderComponent('test query', '', undefined, onAddMessageMock, false, SearchListMode.Chat);
        const form = container.querySelector('form');

        if (form) {
          Simulate.submit(form);
          await waitFor(() => {
            expect(onAddMessageMock).toHaveBeenCalledWith({ sender: 'User', text: 'test query' });
          });
        }
      });

      // 空の値では送信できないことをテスト
      it('入力値が空の場合、送信されない', () => {
        const { container } = renderComponent('', '', undefined, undefined, false, SearchListMode.Chat);
        const form = container.querySelector('form');

        if (form) {
          Simulate.submit(form);
          expect(onSubmitMock).not.toHaveBeenCalled();
        }
      });

      // 空白のみの値では送信できないことをテスト
      it('入力値が空白のみの場合、送信されない', () => {
        const { container } = renderComponent('   ', '', undefined, undefined, false, SearchListMode.Chat);
        const form = container.querySelector('form');

        if (form) {
          Simulate.submit(form);
          expect(onSubmitMock).not.toHaveBeenCalled();
        }
      });
    });
  });

  describe('Reply機能', () => {
    // TestComponentを拡張してreplyプロパティをサポート
    const TestComponentWithReply: React.FC<TestComponentProps & { reply?: string }> = (props) => {
      const {
        initialValue, disabled, searchMode, reply,
      } = props;
      const [value, setValue] = React.useState(initialValue);

      const onChange = React.useCallback((v: string, e: React.SyntheticEvent<HTMLElement>) => {
        setValue(v);
        onChangeMock(v, e);
      }, []);

      React.useEffect(() => {
        currentValueMock(value);
      }, [value]);

      return (
        <SearchInput
          value={value}
          onChange={onChange}
          onSubmit={onSubmitMock}
          disabled={disabled}
          searchMode={searchMode}
          reply={reply}
        />
      );
    };

    TestComponentWithReply.defaultProps = {
      reply: undefined,
    };

    function renderComponentWithReply(
      initialValue: string,
      reply?: string,
      disabled = false,
      searchMode = SearchListMode.Chat,
    ) {
      return render(
        <TestComponentWithReply
          initialValue={initialValue}
          searchMode={searchMode}
          disabled={disabled}
          reply={reply}
        />,
      );
    }

    describe('replyが提供された場合', () => {
      it('replyが提供された時、atTaneからの完了メッセージがonAddMessageでコールバックされる', async () => {
        render(
          <SearchInput
            value=""
            onChange={onChangeMock}
            onSubmit={onSubmitMock}
            disabled={false}
            searchMode={SearchListMode.Chat}
            reply="test reply"
            onAddMessage={onAddMessageMock}
          />,
        );
        await waitFor(() => {
          expect(onAddMessageMock).toHaveBeenCalledWith({ sender: 'atTane', text: '検索が完了しました' });
        });
      });

      // replyが変更された時にメッセージが追加されることをテスト
      it('replyがnullから値に変更された時、完了メッセージがonAddMessageでコールバックされる', async () => {
        const { rerender } = render(
          <SearchInput
            value=""
            onChange={onChangeMock}
            onSubmit={onSubmitMock}
            disabled={false}
            searchMode={SearchListMode.Chat}
            reply={null}
            onAddMessage={onAddMessageMock}
          />,
        );

        rerender(
          <SearchInput
            value=""
            onChange={onChangeMock}
            onSubmit={onSubmitMock}
            disabled={false}
            searchMode={SearchListMode.Chat}
            reply="new reply"
            onAddMessage={onAddMessageMock}
          />,
        );

        await waitFor(() => {
          expect(onAddMessageMock).toHaveBeenCalledWith({ sender: 'atTane', text: '検索が完了しました' });
        });
      });
    });

    describe('replyがnullまたはundefinedの場合', () => {
      // replyがnullの時は完了メッセージが表示されないことをテスト
      it('replyがnullの場合、完了メッセージが表示されない', () => {
        renderComponentWithReply('');
        expect(screen.queryByText('検索が完了しました')).not.toBeInTheDocument();
      });

      // replyがundefinedの時は完了メッセージが表示されないことをテスト
      it('replyがundefinedの場合、完了メッセージが表示されない', () => {
        renderComponentWithReply('', undefined);
        expect(screen.queryByText('検索が完了しました')).not.toBeInTheDocument();
      });
    });
  });

  describe('messagesプロパティ', () => {
    it('messagesが未指定の場合、初期メッセージが表示される', () => {
      renderComponent('', '', undefined, undefined, false, SearchListMode.Chat);
      expect(screen.getByText('お手伝いできることはありますか？')).toBeInTheDocument();
    });
    it('messagesを指定した場合、その内容が表示される', () => {
      const customMessages: Message[] = [
        { sender: 'User', text: 'ユーザーのメッセージ' },
        { sender: 'atTane', text: 'AIの返答' },
      ];
      renderComponent('', '', customMessages, undefined, false, SearchListMode.Chat);
      expect(screen.getByText('ユーザーのメッセージ')).toBeInTheDocument();
      expect(screen.getByText('AIの返答')).toBeInTheDocument();
    });
  });
});
