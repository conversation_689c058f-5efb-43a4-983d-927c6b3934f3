import React from 'react';
import { Button, SettingsIcon } from '@fluentui/react-northstar';

type SettingsButtonProps = {
  onClick?: (toBe: boolean, e: React.SyntheticEvent<HTMLElement>) => void;
  isActive?: boolean;
  className?: string; // 追加！
};

const SettingsButton: React.FC<SettingsButtonProps> = ((props) => {
  const {
    onClick,
    isActive,
    className, // 追加
  } = props;

  // クリックハンドラ
  const handleOnClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (onClick) onClick(!isActive, e);
  }, [onClick, isActive]);

  return (
    <Button
      text
      onClick={handleOnClick}
      className={className} // Buttonに渡す
    >
      <SettingsIcon className="settings-icon" />
    </Button>
  );
});

SettingsButton.defaultProps = {
  onClick: undefined,
  isActive: false,
  className: '', // 追加（なくてもOKだけど）
};

export default SettingsButton;
