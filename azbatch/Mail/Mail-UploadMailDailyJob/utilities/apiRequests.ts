import * as dotenv from 'dotenv';

dotenv.config();

const MAIL_SEARCH_SIZE: string = process.env['MAIL_SEARCH_SIZE'] ?? '50';

interface UserMessageRequest {
  id: string;
  method: string;
  url: string;
}

// *** Create yesterday and today ***
function formatDate(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

function createYesterdayTodayChunks(): Array<{ start: string; end: string }> {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const yesterdayStart = `${formatDate(yesterday)}T00:00:00Z`;
  const todayEnd = `${formatDate(today)}T23:59:59Z`;

  return [{
    start: yesterdayStart,
    end: todayEnd
  }];
}

export function createUserMessageRequests(userId: string): UserMessageRequest[] {
  if (!userId) return [];

  const dateChunks = createYesterdayTodayChunks();

  return [{
    id: `${userId}`,
    method: 'GET',
    url: `/users/${userId}/messages?$top=${MAIL_SEARCH_SIZE}&$filter=receivedDateTime ge ${dateChunks[0].start} and receivedDateTime le ${dateChunks[0].end}&$orderby=receivedDateTime desc`
  }];
}

// export function createUserMessageRequestsDaily(userId: string): UserMessageRequest[] {
//   if (!userId) return [];

//   return [{
//     id: `${userId}`,
//     method: 'GET',
//     url: `/users/${userId}/messages?$top=${MAIL_SEARCH_SIZE}&$filter=receivedDateTime ge ${MAIL_START_DATE}T00:00:00Z and receivedDateTime le ${MAIL_END_DATE}T23:59:59Z&$orderby=receivedDateTime desc`
//   }];
// }