import { createGraphClient } from "../utilities/graph";
import { logger } from "../utilities/log";
import {
  createCredential,
  processListItemsDaily
} from "./impl";
import * as dotenv from "dotenv";
import { validateCosmosDBConnection } from "../utilities/cosmos";
import { siteConfig } from '../spo-config';

dotenv.config();

async function main() {
  logger.log("========== PHASE 1 ==========");
  const credential = createCredential(
    process.env["AzureTenantId"] ?? '',
    process.env["AzureClientId"] ?? '',
    process.env["AzureClientSecret"] ?? ''
  );
  if (!credential) {
    logger.error("[Index:createCredential] NO_CREDENTIALS");
    return Promise.reject(new Error("[Index:createCredential] NO_CREDENTIALS"));
  }
  const graphClient = createGraphClient(credential);

  logger.log("\n========== PHASE 2 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:validateCosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:validateCosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }

  logger.log("\n========== PHASE 3 ==========");
  logger.log("--- Processing Site List Items ---");
  
  const siteListsData = siteConfig;

  await processListItemsDaily(logger, graphClient, siteListsData)
    .catch((error) => {
      logger.error(`[Index:processListItemsDaily] Error: ${error}`);
    });
  logger.log(`[Index:processListItemsDaily] Successfully Inserted ListItems.`);

  logger.log("\n========== PHASE 5 ==========");
  logger.log("--- Finish ---");
  logger.log("[Index] All Phase Successfully Completed.");
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});