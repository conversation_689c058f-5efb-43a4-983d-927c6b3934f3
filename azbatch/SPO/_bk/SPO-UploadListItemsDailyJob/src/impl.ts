import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import {
  createListItemRequestsDaily
} from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchJsonBatchForListItems
} from '../utilities/graph';
import { CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  ISiteListData,
  ISiteLookupData
} from '../utilities/models';
import { encrypt } from '../utilities/encryption';
import {
  insertItemToCosmosDB
} from '../utilities/cosmos';
import { removeHtml } from '../utilities/removeHTML';

const SPO_MAX_GRAPH_API_SITE_LIST_ITEMS_BATCH_COUNTS = parseInt(process.env.SPO_MAX_GRAPH_API_SITE_LIST_ITEMS_BATCH_COUNTS ?? '20');
const SPO_MAX_LIST_ITEMS_CHUNK_SIZE = parseInt(process.env.SPO_MAX_LIST_ITEMS_CHUNK_SIZE ?? '50');

type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 3 - START ========== *******************************************************/
export async function processListItemsDaily(
  logger: Logger,
  client: Client,
  siteListsData: ISiteListData[]
): Promise<void> {
  
  logger.log(`[Impl:processListItemsDaily] Total siteListsData to Process: ${siteListsData.length}`);
  logger.log(`[Impl:processListItemsDaily] siteListsData: ${JSON.stringify(siteListsData)}`);

  const siteListsLookup = new Map<string, ISiteLookupData>();
  siteListsData.forEach(data => {
    siteListsLookup.set(data.id, {
      groupIds: data.groupIds,
      siteName: data.siteName,
      listName: data.listName
    });
  });

  const listItemsBatchRequestsCreated: BatchRequestData[] = siteListsData
    .filter(data => data.siteId && data.listId)
    .flatMap((data) =>
      createListItemRequestsDaily(data?.id ?? '', data?.siteId ?? '', data?.listId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url,
      }))
    );

  logger.log(`[Impl:processListItemsDaily] Created ${listItemsBatchRequestsCreated.length} Requests`);

  const splitListItemsBatchRequests = splitArrayIntoChunks(listItemsBatchRequestsCreated, SPO_MAX_GRAPH_API_SITE_LIST_ITEMS_BATCH_COUNTS);
  logger.log(`[Impl:processListItemsDaily] Total Split Batch: ${splitListItemsBatchRequests.length} | SPO_MAX_GRAPH_API_SITE_LIST_ITEMS_BATCH_COUNTS: ${SPO_MAX_GRAPH_API_SITE_LIST_ITEMS_BATCH_COUNTS}`);
  logger.log(`[Impl:processListItemsDaily] splitListItemsBatchRequests: ${JSON.stringify(splitListItemsBatchRequests)}`);  // !!!

  const totalListItemsBatchRequests = splitListItemsBatchRequests.length;
  for (let i = 0; i < totalListItemsBatchRequests; i++) {
    try {
      const currentListItemsBatchRequests = i + 1;
      logger.log(`\n---- START BATCH | API REQUEST: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests} ----`);
      
      const currentSplitItemsBatchRequests = splitListItemsBatchRequests[i];
      // logger.log(`[Impl:processListItemsDaily] splitListItemsBatchRequests_inside == ${JSON.stringify(splitListItemsBatchRequests)}`); // !!!
      // logger.log(`[Impl:processListItemsDaily] currentSplitItemsBatchRequests == ${JSON.stringify(currentSplitItemsBatchRequests)}`); // !!!

      const batchResultListItems = await fetchJsonBatchForListItems(logger, client, currentSplitItemsBatchRequests);
      // // *** View Raw Response
      // logger.log(`*** [Impl:processListItemsDaily] batchResultListItems == ${JSON.stringify(batchResultListItems)}`); // !!!

      if (!batchResultListItems.responses || batchResultListItems.responses.length === 0) {
        logger.log(`[Impl:processListItemsDaily] No Responses in API REQUEST: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests}`);
        continue;
      }

      await processBatchResultListItems(logger, batchResultListItems, currentListItemsBatchRequests, totalListItemsBatchRequests, siteListsLookup);

      batchResultListItems.responses = [];
      splitListItemsBatchRequests[i] = [];
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      logger.log(`---- END BATCH | API REQUEST: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests} ----\n`);
      
    } catch (error) {
      logger.error(`[Impl:processListItemsDaily] Error Processing API Batch ${i + 1}: ${error}`);
      continue;
    }
  }
  listItemsBatchRequestsCreated.length = 0;
  splitListItemsBatchRequests.length = 0;
}

async function processBatchResultListItems(
  logger: Logger,
  batchResultListItems: IBatchResponses,
  currentListItemsBatchRequests: number,
  totalListItemsBatchRequests: number,
  siteListsLookup: Map<string, ISiteLookupData>
): Promise<void> {

  const totalBatchResultListItems = batchResultListItems.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultListItems; j++) {
    const currentBatchResultListItems = j + 1;
    const batchResultListItemsResponses = (batchResultListItems.responses ?? [])[j];
    const siteListId = batchResultListItemsResponses.id;
    
    logger.log(`\n\n**** START BATCH | PROCESSSING ITEM: (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests}) ****`);

    const siteData = siteListsLookup.get(siteListId ?? "");
    const currentGroupIds = siteData?.groupIds;
    const currentSiteName = siteData?.siteName;
    const currentListName = siteData?.listName;
    
    logger.log(`[Impl:processBatchResultListItems] Site Name: ${currentSiteName}`);
    logger.log(`[Impl:processBatchResultListItems] List Name: ${currentListName}`);
    logger.log(`[Impl:processBatchResultListItems] GroupID's for ${siteListId}: ${JSON.stringify(currentGroupIds)}`);
    
    if (!batchResultListItemsResponses || batchResultListItemsResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultListItems] Skipping siteListId: ${siteListId} with Error Data: ${batchResultListItemsResponses?.status} === (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests})`);
      continue;
    }

    const singleResultListItems = { responses: [batchResultListItemsResponses] };
    // logger.info(`[Impl:processBatchResultListItems] singleResultListItems == ${JSON.stringify(singleResultListItems)} === (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests})`);  // !!!
                                                                      
    const modifiedListItemsChunk = processSingleResultListItems([singleResultListItems], currentSiteName ?? '', currentListName ?? '', currentGroupIds ?? [], logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultListItems] *** modifiedListItemsChunk == ${JSON.stringify(modifiedListItemsChunk)} === (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests})`); // !!!

    const totalmodifiedListItemsChunk = modifiedListItemsChunk.length;
    
    // Inserting Start...
    for (let k = 0; k < totalmodifiedListItemsChunk; k++) {
      const itemChunk = modifiedListItemsChunk[k];
      const currentmodifiedListItemsChunk = k + 1;
      if (!itemChunk) {
        logger.info(`[Impl:processBatchResultListItems] Skipping Undefined ListItems at Index: ${k} === (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests})`);
        continue;
      }
      logger.log(`++++ START BATCH | INSERTING ITEM IN COSMOS_DB: (Chunk: ${currentmodifiedListItemsChunk} of ${totalmodifiedListItemsChunk} with ${itemChunk.body?.value?.length} ListItems Inside) ++++`);
      logger.log(`++++ (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests}) ++++`);
      try {
        logger.info(`[Impl:processBatchResultListItems] Inserting ListItems...`);
        await insertItemToCosmosDB(logger, [itemChunk]);
        logger.info(`[Impl:processBatchResultListItems] Successfully Inserted ListItems...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultListItems] Failed Inserting: ${error} (Chunk: ${currentmodifiedListItemsChunk} of ${totalmodifiedListItemsChunk} with ${itemChunk.body?.value?.length} ListItems Inside) === (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests})`);
        continue;
      }
      modifiedListItemsChunk[k] = {} as IBatchResponseData;
      logger.log(`++++ (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests}) ++++`);
      logger.log(`++++ END BATCH | INSERTING ITEM IN COSMOS_DB: (Chunk: ${currentmodifiedListItemsChunk} of ${totalmodifiedListItemsChunk} with ${itemChunk.body?.value?.length} ListItems Inside) ++++`);
    }
    // Inserting End...

    modifiedListItemsChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`**** END BATCH | PROCESSSING ITEM: : (siteListId: ${siteListId} / Batch Result: ${currentBatchResultListItems} of ${totalBatchResultListItems} / Batch Request: ${currentListItemsBatchRequests} of ${totalListItemsBatchRequests}) ****`);
  }
}

function processSingleResultListItems(
  singleResItem: IBatchResponses[],
  currentSiteName: string,
  currentListName: string,
  currentGroupIds: string[],
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResItem) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultListItems] Skipping Invalid Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalListItems = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultListItems] Total response.body.value: ${totalListItems}`);

      // const siteListId = response.id ? response.id.split('-month')[0] : ''; // Extract everything before "-month"
      const siteListId = response.id ? response.id : '';
      const allListItems = response.body?.value as any[];
      // logger.log(`[Impl:processSingleResultListItems] allListItems: ${JSON.stringify(allListItems)}`);  // !!!

      // Process in chunks
      const chunkSize = SPO_MAX_LIST_ITEMS_CHUNK_SIZE;
      for (let i = 0; i < allListItems.length; i += chunkSize) {
        const listItemsAfterChunk = allListItems.slice(i, i + chunkSize);
        // slogger.info(`[Impl:processSingleResultListItems] listItemsAfterChunk: ${JSON.stringify(listItemsAfterChunk)}`); // !!!

        processListItemsChunk(siteListId, listItemsAfterChunk, allProcessedData, currentSiteName, currentListName, currentGroupIds,logger);
        listItemsAfterChunk.length = 0;
      }
      allListItems.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultMails] allProcessedData: ${allProcessedData}`); // !!!
  return allProcessedData;
}

function processListItemsChunk(
  siteListId: string,
  listItemsAfterChunk: any[],
  allProcessedData: IBatchResponseData[],
  currentSiteName: string,
  currentListName: string,
  currentGroupIds: string[],
  logger: Logger
): void {
  const processedValues: any[] = [];
  let skippedCount = 0;

  for (const item of listItemsAfterChunk) {
    if (hasEmptyRequiredFields(item, logger)) {
      skippedCount++;
      continue;
    }
    processedValues.push(createEncryptedListItems(item, currentSiteName, currentListName, currentGroupIds, logger));
  }

  // Log the skipped count if any items were skipped
  if (skippedCount > 0) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipped ${skippedCount} ListItems with Null Values in this chunk`);
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: siteListId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processTeams1on1GroupChatsMessagesChunk] Successfully Modified: ${processedValues.length} ListItems Inside Chunk | SPO_MAX_LIST_ITEMS_CHUNK_SIZE: ${SPO_MAX_LIST_ITEMS_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFields(item: any, logger: Logger): boolean {
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  return isEmpty(item.sharepointIds.listItemUniqueId);
}

function createEncryptedListItems(
  item: any,
  currentSiteName: string,
  currentListName: string,
  currentGroupIds: string[],
  logger: Logger
): any {
  let properties: Record<string, any> | null = null;

  let listItemUniqueId = null
  listItemUniqueId = item.sharepointIds.listItemUniqueId;

  let listUrlValue = ''
  let siteUrlValue = ''

  let listIdValue = item.sharepointIds.listId;
  let itemIdValue = item.sharepointIds.listItemId;
  let editLinkValue = `Web/Lists(guid'${listIdValue}')/Items(${itemIdValue})`;

  // Extract listUrl and siteUrl from webUrl
  if (item.webUrl) {
    try {
      const url = new URL(item.webUrl);
      
      // Extract the path parts
      const pathParts = url.pathname.split('/');
      
      // Find the index of 'Lists' in the path
      const listsIndex = pathParts.findIndex(part => part === 'Lists');
      
      if (listsIndex !== -1 && listsIndex + 1 < pathParts.length) {
        // Create the DispForm.aspx URL
        const siteUrlPath = pathParts.slice(0, listsIndex).join('/');
        const listPath = pathParts.slice(0, listsIndex + 2).join('/');

        listUrlValue = `${url.protocol}//${url.host}${listPath}/DispForm.aspx`;
        siteUrlValue = `${url.protocol}//${url.host}${siteUrlPath}/`;
      }
    } catch (error) {
      logger.log(`[Impl:createEncryptedListItems] Error Parsing webUrl: ${error}`);
    }
  }

  const SPO_CUSTOM_BODY_FIELDS = JSON.parse(process.env.SPO_CUSTOM_BODY_FIELDS ?? '[]');
  const SPO_CUSTOM_FIELDS = JSON.parse(process.env.SPO_CUSTOM_FIELDS ?? '[]');

  if (item?.fields) {
    properties = {
      createdDate: item.fields.Created ?? null,
      editLink: encrypt(editLinkValue) ?? null,
      hasAttachments: item.fields.Attachments,
      siteName: encrypt(currentSiteName) ?? null,
      listName: encrypt(currentListName) ?? null,
      listId: encrypt(listIdValue) ?? null,
      listUrl: encrypt(listUrlValue) ?? null,
      siteUrl: encrypt(siteUrlValue) ?? null,
      updatedDate: item.fields.Modified ?? null,
      title: encrypt(item.fields.Title) ?? null,
    };

    // Custom Body Field
    const bodyField = SPO_CUSTOM_BODY_FIELDS.find((fieldName: string) => 
      item.fields[fieldName] !== undefined && item.fields[fieldName] !== null
    );
    properties.body = bodyField ? encrypt(removeHtml(item.fields[bodyField])) : null;

    // Custom Fields 
    SPO_CUSTOM_FIELDS.forEach((fieldName: string) => {
      if (properties) {
        // Check if field exists and has a value
        if (item.fields[fieldName] !== undefined && item.fields[fieldName] !== null) {
          // Don't encrypt specific fields
          if (fieldName === 'releaseState' || fieldName === 'presentPeriod') {
            properties[fieldName] = item.fields[fieldName];
          } else {
            properties[fieldName] = encrypt(item.fields[fieldName]) ?? null;
          }
        } else {
          properties[fieldName] = null;
        }
      }
    });
  }

  return {
    security_user_id: currentGroupIds,
    id: listItemUniqueId ?? null,
    kind: "SPO",
    properties,
  };
}
/******************************************************* ========== PHASE 3 - END ========== *********************************************************/