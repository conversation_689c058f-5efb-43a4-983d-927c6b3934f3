import * as cheerio from "cheerio";

/**
 * Removes HTML tags and content from a string, returning clean text
 * @param htmlText - The HTML string to clean
 * @returns Clean text with HTML removed
 * @throws Error if HTML removal fails
 */
export function removeHtml(htmlText: string): string {
  try {
    if (!htmlText || typeof htmlText !== 'string') {
      return '';
    }

    // Pre-clean
    let preCleanedHtml = htmlText
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');

    const $html = cheerio.load(preCleanedHtml);
    
    // Remove HTML Elements
    $html('script').remove();
    $html('style').remove();
    $html('noscript').remove();
    $html('meta').remove();
    $html('title').remove();
    $html('head').remove();
    $html('link').remove();
    $html('svg').remove();
    $html('img').remove();
    $html('code').remove();
    $html('pre').remove();
    $html('details').remove();
    
    $html('*').contents().filter(function() {
      return this.nodeType === 8;
    }).remove();
    
    $html('div, p, h1, h2, h3, h4, h5, h6, li, td, th, br').before(' ');
    
    // Get clean text
    let cleanText = $html.text();
    
    // Clean up whitespace
    cleanText = cleanText
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .replace(/\\"/g, '"')
      .replace(/\\n/g, ' ')
      .replace(/\\/g, '')
      .trim();
    
    return cleanText;
  } catch (error) {
    throw new Error('HTML removal failed: ' + (error instanceof Error ? error.message : 'Unknown Error'));
  }
}

/**
 * Processes multiple HTML strings and removes HTML from each
 * @param htmlStrings - Array of HTML strings to process
 * @returns Array of clean text strings
 */
export function removeHtmlBatch(htmlStrings: string[]): string[] {
  return htmlStrings.map(html => {
    try {
      return removeHtml(html);
    } catch (error) {
      // Return empty string for failed items instead of throwing
      console.warn('Failed to remove HTML from item:', error instanceof Error ? error.message : 'Unknown error');
      return '';
    }
  });
}

/**
 * Asynchronously processes multiple HTML strings with optional progress callback
 * @param htmlStrings - Array of HTML strings to process
 * @param options - Processing options
 * @returns Promise resolving to array of clean text strings
 */
export async function removeHtmlBatchAsync(
  htmlStrings: string[], 
  options?: {
    batchSize?: number;
    onProgress?: (processed: number, total: number) => void;
  }
): Promise<string[]> {
  const { batchSize = 100, onProgress } = options || {};
  const results: string[] = [];
  
  for (let i = 0; i < htmlStrings.length; i += batchSize) {
    const batch = htmlStrings.slice(i, i + batchSize);
    
    const batchResults = await Promise.allSettled(
      batch.map(async (html) => removeHtml(html))
    );
    
    const processedBatch = batchResults.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.warn(`Failed to process item at index ${i + index}:`, result.reason);
        return '';
      }
    });
    
    results.push(...processedBatch);
    
    if (onProgress) {
      onProgress(Math.min(i + batchSize, htmlStrings.length), htmlStrings.length);
    }
    
    // Allow event loop to process other tasks
    await new Promise(resolve => setImmediate(resolve));
  }
  
  return results;
}