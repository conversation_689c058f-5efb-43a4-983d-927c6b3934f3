import { Client, PageCollection, BatchRequestData, PageIteratorCallback, PageIterator } from "@microsoft/microsoft-graph-client";
import { TokenCredential } from '@azure/core-auth';
import { TokenCredentialAuthenticationProvider } from '@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials';
import { IBatchResponseData, IBatchResponses, ISiteListData } from './models';
import { strToNum } from './number';
import { waitForTime } from './promise';
import { logger } from './log';

export interface ISearchQuery {
  requests: {
    entityTypes: string[],
    query: {
      queryString: string,
    },
    from?: number,
    size?: number,
  }[],
};
type GroupedResult = { noRetry: IBatchResponseData[], retry: IBatchResponseData[] };
type LoggerType = typeof logger;

const GRAPH_STATUS_CODES_TO_RETRY = [429, 503, 504]; // 再試行が必要なHTTPステータスコードを含む配列
const RETRY_AFTER_DEFAULT_SEC = 3; // 試行の間隔が指定されていない場合に使用される、デフォルトの再試行間隔
const MILLI_SEC = 1000; // 秒をミリ秒に変換するための定数
const BATCH_MAX_RETRY = 3; // バッチの最大リトライ回数
const RETRY_AFTER_WAIT_COEFFICIENT = 1.5; // リトライ後の待機時間を長くし、負担を軽減させるための定数

/**
 * GraphAPI用のクライアントを生成
 * @param credential
 */
export function createGraphClient(
  credential: TokenCredential,
): Client {
  return Client.initWithMiddleware({
    debugLogging: true,
    authProvider: new TokenCredentialAuthenticationProvider(
      credential,
      // TODO:scopeをenvに設定する
      { scopes: ['https://graph.microsoft.com/.default'] },
    ),
  });
}

/**
 * group responses to retry or not
 * @param responses Graph API Batchリクエストのレスポンス1件分
 */
function getGroupedResult(responses: IBatchResponseData[]) {
  return responses.reduce<GroupedResult>((result, entry) => {
    const key = GRAPH_STATUS_CODES_TO_RETRY.includes(Number(entry.status)) ? 'retry' : 'noRetry';
    return {
      ...result,
      [key]: [
        ...result[key],
        entry,
      ],
    };
  }, { retry: [], noRetry: [] });
}

/**
 * post json batch request and retry for 3 times if needed
 * @param logger
 * @param client
 * @param requests
 * @param count a count of reclusive run; default value is 1; avoid next looping when the count is 3
 */
export async function fetchJsonBatchForListItems(
  logger: LoggerType, 
  spoToken: string,
  requests: any[],
  count = 0,
): Promise<IBatchResponses> {

  if (count >= 1) {
    // log count on retrying
    logger.log(`RetryingCount: ${count}`);
  }

  // Execute all requests in parallel
  const result = {
    responses: await Promise.all(
    requests.map(async (request) => {
      const spoRequestUrl = `${request.url}`;
      logger.log("[Graph:fetchListItems] spoRequestUrl:", spoRequestUrl);

      try {
        const response = await fetch(spoRequestUrl, {
          headers: {
            'Authorization': `Bearer ${spoToken}`,
            'Accept': 'application/json'
          }
        });

        const responseHeaders: Record<string, string> = {};
        response.headers.forEach((value, key) => {
          responseHeaders[key] = value;
        });

        if (!response.ok) {
          const errorText = await response.text();
          logger.error(`[Graph:fetchJsonBatchForItem] Error Processing: ${request.id}:`, errorText);
          return {
            id: request.id,
            status: response.status,
            headers: responseHeaders,
            body: { error: errorText }
          };
        }

       const result = await response.json();
        return {
          id: request.id,
          status: response.status,
          headers: responseHeaders,
          body: result
        };

      } catch (error) {
        logger.error(`[Graph:fetchJsonBatchForItem] Unknown Error: ${request.id}:`, error);
        return {
          id: request.id,
          status: 500,
          headers: {},
          body: { error: error instanceof Error ? error.message : 'Unknown Error' }
        };
      }
    })
  )} as IBatchResponses;

  // logger.error(`[Graph:fetchListItems] result:`, JSON.stringify(result));
  if (!Array.isArray(result.responses)) return result;

  // logger.log("[Graph:fetchJsonBatchForItem] result.responses:", result.responses);
  // const groupedResult = getGroupedResult(result.responses);

  // Start Pagination ...
  const paginatedResponses: IBatchResponseData[] = await Promise.all(
    result.responses.map(async (response) => {
      try {
        logger.info(`[Graph:fetchJsonBatchForItem] Processing Batch: ${response.id}`);

        if (response.status === 403) {
          logger.info(`[Graph:fetchJsonBatchForItem] Unauthorized, Skipping: ${response.id} - Status: ${response.status}`);
          return response;
        }
        if (response.status !== 200) {
          logger.info(`[Graph:fetchJsonBatchForItem] Skipping: ${response.id} - Status: ${response.status}`);
          return response;
        }
        if (!response.body) {
          logger.info(`[Graph:fetchJsonBatchForItem] Skipping: ${response.id} - No Response Body`);
          return response;
        }

        if ('odata.nextLink' in response.body) {
          const allItems: any[] = [...(response.body.value || [])];
          let nextUrl = response.body['odata.nextLink'];

          logger.info(`[Graph:fetchListItems] Starting Pagination for: ${response.id}`);
          while (nextUrl) {
            const nextResponse = await fetch(nextUrl as string, {
              headers: {
                'Authorization': `Bearer ${spoToken}`,
                'Accept': 'application/json'
              }
            });
            if (!nextResponse.ok) break;
            const nextResult = await nextResponse.json();
            allItems.push(...(nextResult.value || []));
            nextUrl = nextResult['odata.nextLink'];
            // Add delay to avoid throttling
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          logger.info(`[Graph:fetchListItems] Finished Pagination for: ${response.id} | Total: ${allItems.length}`);

          return {
            ...response,
            body: {
              value: allItems,
              'odata.nextLink': undefined
            }
          } as IBatchResponseData;
        } else {
          logger.info(`[Graph:fetchJsonBatchForItem] No pagination needed for: ${response.id} | Total: ${response.body.value?.length}`);
          return response;
        }
      } catch (error) {
        logger.error(`[Graph:fetchJsonBatchForItem] Error Processing: ${response.id}:`, error);
        return {
          ...response,
          status: 500,
          body: {
            error: error instanceof Error ? error.message : 'Unknown Error'
          }
        } as IBatchResponseData;
      }
    })
  );
  // logger.log("[Graph:fetchJsonBatchForItem] paginatedResponses:", paginatedResponses);
  const groupedResult = getGroupedResult(paginatedResponses);
  // End Pagination ...

  // exit when the retry length is 0 || the count value is equal or grater than BATCH_MAX_RETRY
  if (count >= BATCH_MAX_RETRY || groupedResult.retry.length === 0) {
    if (groupedResult.retry.length !== 0) {
      logger.log(`[Graph:fetchJsonBatchForItem] EntriesFailedToRetry: ${groupedResult.retry.length}, JSON: ${JSON.stringify(groupedResult.retry)}`);
    }
    return { responses: groupedResult.noRetry };
  }

  // log failed requests
  logger.log(`[Graph:fetchJsonBatchForItem] FailedBatchRequests: ${groupedResult.retry.length}, JSON: ${JSON.stringify(groupedResult.retry)}`);

  // prepare for the next loop

  // get a max value of Retry-After from the failed responses
  const maxRetryAfter = Math.max(RETRY_AFTER_DEFAULT_SEC, ...groupedResult.retry.map((d) => {
    return strToNum(d.headers?.['Retry-After'], 0);
  }));

  // create a dictionary from the parameter to find failed parameter
  const reqDict = requests.reduce<Record<string, BatchRequestData>>((dict, request) => {
    return { ...dict, [request.id]: request };
  }, {});

  // create a parameter for the next fetch
  const nextRequests = groupedResult.retry.map((d) => {
    return reqDict?.[d.id ?? ''];
  }).filter((r) => !!r);

  // wait some Retry-After seconds to throttle requests
  await waitForTime(maxRetryAfter * RETRY_AFTER_WAIT_COEFFICIENT * MILLI_SEC);

  // retry recursively
  const nextResult = await fetchJsonBatchForListItems(logger, spoToken, nextRequests, count + 1);

  // return result joined with current response and retry response
  return {
    responses: [
      ...groupedResult.noRetry,
      ...Array.isArray(nextResult.responses)
        ? nextResult.responses
        : [],
    ]
  };
}