import { Container, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData } from "./models";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertItemToCosmosDB(
  logger: CustomLogger,
  dataItem: IBatchResponseData[],
): Promise<void> {

  // logger.log(`[CosmosDB:Insert] dataItem: ${JSON.stringify(dataItem)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Insert] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedListItems: any[][] = dataItem
      .map((listItem) => listItem.body?.value || [])
      .filter(Array.isArray);

    const insertedCount = await processItem(container, modifiedListItems, logger);

    logger.log(`[CosmosDB:Insert] Inserted: ${insertedCount} New ListItem to Cosmos DB`);
    const totalMessageCount = modifiedListItems.reduce((count, array) => count + (array ? array.length : 0),0 );
    logger.log(`[CosmosDB:Insert] Skipped: ${totalMessageCount - insertedCount} Existing ListItem`);
  } catch (error) {
    logger.log(`[CosmosDB:Insert] Error Processing: ${error}`);
    throw error;
  }
}

async function processItem(
  container: Container,
  modifiedListItems: any[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  // logger.log(`[CosmosDB:Process] modifiedListItems: ${JSON.stringify(modifiedListItems)}`); // !!!

  for (const messageArray of modifiedListItems) {
    for (const listItem of messageArray) {
      if (!listItem.id) {
        logger.log(`[CosmosDB:Process] Error: listItem is undefined for listItem: ${JSON.stringify(listItem)}`);
        continue;
      }

      // Check releaseState
      if (listItem.properties.releaseState !== '公開') {
        logger.log(`[CosmosDB:Process] Skipping listItem: ${listItem.id} | releaseState is not '公開' (current: ${listItem.properties.releaseState})`);
        continue;
      }

      // Check presentPeriod
      const currentDateTimeUTC = new Date();
      if (listItem.properties.presentPeriod !== null) {
          const presentPeriodDate = new Date(listItem.properties.presentPeriod);
          
          const currentDateString = currentDateTimeUTC.toISOString();
          const presentPeriodDateString = presentPeriodDate.toISOString();
          // logger.log(`[CosmosDB:Process] currentDate UTC: ${currentDateString}`);
          // logger.log(`[CosmosDB:Process] listItem ${listItem.id}: presentPeriod date ${presentPeriodDateString}`);

          // Skip if presentPeriod date is BEFORE currentDateTimeUTC
          if (presentPeriodDateString <= currentDateString) {
              logger.log(`[CosmosDB:Process] Skipping listItem: ${listItem.id} | presentPeriod date is expired (current: ${presentPeriodDateString})`);
              continue;
          }
      }

      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: listItem.id }]
      };
      
      try {
        const { resources: existingListItem } = await container.items.query(querySpec).fetchAll();
        if (existingListItem.length === 0) {
          await container.items.create(listItem);
          insertedCount++;
        }
      } catch (error) {
        logger.log(`[CosmosDB:Process] Error processing listItem: ${error}`);
      }
    }
  }
  return insertedCount;
}