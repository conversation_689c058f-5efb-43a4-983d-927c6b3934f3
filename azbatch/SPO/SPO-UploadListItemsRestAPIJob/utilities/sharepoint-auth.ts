import * as fs from 'fs';
import * as jwt from 'jsonwebtoken';
import * as forge from 'node-forge';
import { v4 as uuidv4 } from 'uuid';
import { CustomLogger } from "./log";

interface SharePointAuthConfig {
  clientId: string;
  tenantId: string;
  certificatePath: string;
  certificatePassword: string;
  sharepointHostname: string;
}

interface SharePointTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

interface SharePointAuthResult {
  success: boolean;
  accessToken?: string;
  expiresAt?: Date;
  error?: string;
}

// Generate JWT Token
async function generateJwtToken(config: SharePointAuthConfig): Promise<string> {
  try {
    // Read and parse certificate
    const certBuffer = fs.readFileSync(config.certificatePath);
    const p12Asn1 = forge.asn1.fromDer(certBuffer.toString('binary'));
    const p12 = forge.pkcs12.pkcs12FromAsn1(p12Asn1, config.certificatePassword);
    
    // Extract private key
    const bags = p12.getBags({ bagType: forge.pki.oids.pkcs8ShroudedKeyBag });
    const bag = bags[forge.pki.oids.pkcs8ShroudedKeyBag]?.[0];
    if (!bag?.key) {
      throw new Error('Could not extract private key from certificate');
    }
    const privateKey = forge.pki.privateKeyToPem(bag.key);
    
    // Extract certificate for thumbprint
    const certBags = p12.getBags({ bagType: forge.pki.oids.certBag });
    const certBag = certBags[forge.pki.oids.certBag]?.[0];
    if (!certBag?.cert) {
      throw new Error('Could not extract certificate from PFX');
    }
    
    // Calculate certificate thumbprints
    const certDer = forge.asn1.toDer(forge.pki.certificateToAsn1(certBag.cert)).getBytes();
    const sha1Hash = forge.md.sha1.create();
    sha1Hash.update(certDer);
    const sha1Thumbprint = forge.util.encode64(sha1Hash.digest().getBytes()).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    const sha256Hash = forge.md.sha256.create();
    sha256Hash.update(certDer);
    const sha256Thumbprint = forge.util.encode64(sha256Hash.digest().getBytes()).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    // Create JWT payload
    const payload = {
      aud: `https://login.microsoftonline.com/${config.tenantId}/v2.0/`,
      iss: config.clientId,
      sub: config.clientId,
      jti: uuidv4(),
      nbf: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
    };
    
    // Sign JWT with certificate thumbprints
    const token = jwt.sign(payload, privateKey, {
      algorithm: 'RS256',
      header: {
        alg: 'RS256',
        typ: 'JWT',
        x5t: sha1Thumbprint,
        'x5t#S256': sha256Thumbprint
      }
    });
    
    return token;
  } catch (error) {
    throw new Error(`JWT Generation Failed: ${error}`);
  }
}

// Exchange JWT for Access Token
async function exchangeJwtForAccessToken(
  config: SharePointAuthConfig, 
  jwtToken: string
): Promise<SharePointTokenResponse> {
  try {
    const tokenUrl = `https://login.microsoftonline.com/${config.tenantId}/oauth2/v2.0/token`;
    const scope = `https://${config.sharepointHostname}.sharepoint.com/.default`;
    
    const body = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: config.clientId,
      client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
      client_assertion: jwtToken,
      scope: scope
    });
    
    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'content-type': 'application/x-www-form-urlencoded'
      },
      body: body.toString()
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token exchange failed: ${response.status} - ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    throw new Error(`Access token exchange failed: ${error}`);
  }
}

export async function getSharePointAccessToken(
  logger: CustomLogger,
  config: SharePointAuthConfig, 
  verbose: boolean = false
): Promise<SharePointAuthResult> {
  try {
    if (verbose) {
      logger.log('Getting SharePoint Access Token...');
      logger.log(`Tenant: ${config.sharepointHostname}.sharepoint.com`);
    }
    
    //  Generate JWT
    if (verbose) logger.log('Generating JWT Token...');
    const jwtToken = await generateJwtToken(config);
    if (verbose) logger.log('Successfully Generated - JWT Token');
    
    // Exchange for Access Token
    if (verbose) logger.log('Exchanging JWT Token for Sharepoint Access Token...');
    const tokenResponse = await exchangeJwtForAccessToken(config, jwtToken);
    if (verbose) logger.log('Sharepoint Access Token Obtained');
    
    const expiresAt = new Date(Date.now() + (tokenResponse.expires_in * 1000));
    
    if (verbose) {
      logger.log(`⏰ Token expires at: ${expiresAt.toISOString()}`);
      logger.log('🎉 Authentication successful!');
    }
    
    return {
      success: true,
      accessToken: tokenResponse.access_token,
      expiresAt: expiresAt
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    if (verbose) {
      logger.error(`SharePoint Authentication Failed: ${errorMessage}`);
    }
    
    return {
      success: false,
      error: errorMessage
    };
  }
}

export async function createSharePointClient(
  logger: CustomLogger,
  config: SharePointAuthConfig,
  verbose: boolean = false
) {
  const authResult = await getSharePointAccessToken(logger, config, verbose);
  
  if (!authResult.success || !authResult.accessToken) {
    throw new Error(`SharePoint authentication failed: ${authResult.error}`);
  }
  
  const baseUrl = `https://${config.sharepointHostname}.sharepoint.com`;
  
  return {
    token: authResult.accessToken,
    baseUrl: baseUrl,
    expiresAt: authResult.expiresAt,
    
    async makeRequest(endpoint: string, options: RequestInit = {}) {
      const url = `${baseUrl}${endpoint}`;
      return fetch(url, {
        ...options,
        headers: {
          'Authorization': `Bearer ${authResult.accessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });
    }
  };
}