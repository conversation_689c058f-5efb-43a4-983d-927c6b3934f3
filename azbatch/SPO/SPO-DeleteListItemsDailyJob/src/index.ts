import { logger } from "../utilities/log";
import * as dotenv from "dotenv";
import { deleteAllDocumentsFromCosmosDB } from "../utilities/cosmos";

dotenv.config();

async function main() {
  logger.log("\n========== PHASE 1 ==========");
  logger.log("--- Deleting CosmosDB Data ---");
  try {
    await deleteAllDocumentsFromCosmosDB(logger);
    logger.log("[Index:ProcessUserMessages] Successfully Deleted All Data.");
  } catch (error) {
    logger.error(error);
  }
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});