import { CosmosClient } from "@azure/cosmos";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosD<PERSON><PERSON>ey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function deleteAllDocumentsFromCosmosDB(logger: CustomLogger): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Delete] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const database = client.database(databaseName);
    const container = database.container(containerName);

    logger.log(`[CosmosDB:Delete] Deleting Entire Container: ${containerName}`);

    await container.delete();
    await database.containers.createIfNotExists({ 
      id: containerName,
      partitionKey: "/id"
    });

    logger.log(`[CosmosDB:Delete] All Data Deleted. Container Recreated.`);
  } catch (error) {
    logger.log(`[CosmosDB:Delete] Error: ${error}`);
    throw error;
  }
}