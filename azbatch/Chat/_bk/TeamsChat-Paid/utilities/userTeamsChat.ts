import * as dotenv from 'dotenv';

dotenv.config();

const TEAMSCHAT_SEARCH_SIZE: string = process.env['TEAMSCHAT_SEARCH_SIZE'] ?? '20';
const MESSAGE_START_DATE: string = process.env['MESSAGE_START_DATE'] ?? '2025-03-09';
const MESSAGE_END_DATE: string = process.env['MESSAGE_END_DATE'] ?? '2025-03-10';

interface UserMessageRequest {
  id: string;
  method: string;
  url: string;
}

export function createUserTeamsChatRequests(userId: string): UserMessageRequest[] {
  if (!userId) return [];

  return [{
    id: userId,
    method: 'GET',
    url: `/users/${userId}/chats/getAllMessages?top=${TEAMSCHAT_SEARCH_SIZE}&$filter=lastModifiedDateTime gt ${MESSAGE_START_DATE}T00:00:00Z and lastModifiedDateTime lt ${MESSAGE_END_DATE}T23:59:59Z`
  }];
}