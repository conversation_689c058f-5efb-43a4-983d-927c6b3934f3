import { Container, CosmosClient } from "@azure/cosmos";
import {
  IBatchResponseData,
  ITeamsChatData
} from './models';
import { CustomLogger } from './log';
import * as dotenv from 'dotenv';

dotenv.config();

const cosmosDBEndpoint = process.env['COSMOS_DB_ENDPOINT'];
const cosmosDBKey = process.env['COSMOS_DB_KEY'];
const databaseName = process.env['COSMOS_DB_DATABASE'];
const containerName = process.env['COSMOS_DB_CONTAINER'];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:Client] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log('[CosmosDB:Client] Initializing Client Connection...');
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey
      });
      logger.log('[CosmosDB:Client] Client Initialized Successfully');
    } else {
      logger.log('[CosmosDB:Client] Reusing Existing Connection');
    }

    return cosmosClient;

  } catch (error) {
    logger.log(`[CosmosDB:Client] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger,
): Promise<void> {

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Validate] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:Validate] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases.readAll().fetchAll();
    logger.log(`[CosmosDB:Validate] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:Validate] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:Validate] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Container resource is undefined");
    }

    // Count Container Details
    const { resources: [count] } = await container.items
      .query("SELECT VALUE COUNT(1) FROM c")
      .fetchAll();
    logger.log(`[CosmosDB:Validate] Container has: ${count} Items`);

  } catch (error) {
    logger.log(`[CosmosDB:Validate] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertTeamsChatToCosmosDB(
  logger: CustomLogger,
  userMessages: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:Insert] userMessages: ${JSON.stringify(userMessages)}`);
  
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Insert] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    // Extract the body.value
    const modifiedMessages: ITeamsChatData[][] = userMessages
      .map(message => message.body?.value || [])
      .filter(Array.isArray);
    const insertedCount = await processMessages(container, modifiedMessages, logger);

    logger.log(`[CosmosDB:Insert] Inserted ${insertedCount} New TeamsChat to Cosmos DB`);

    const totalMessageCount = modifiedMessages.reduce((count, array) => count + (array ? array.length : 0), 0);
    logger.log(`[CosmosDB:Insert] Skipped ${totalMessageCount - insertedCount} Existing TeamsChat`);

  } catch (error) {
    logger.log(`[CosmosDB:Insert] Error TeamsChat: ${error}`);
    throw error;
  }
}

async function processMessages(
  container: Container,
  modifiedMessages: ITeamsChatData[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  // logger.log(`[CosmosDB:Insert] modifiedMessages: ${JSON.stringify(modifiedMessages)}`);
  
  for (const messageArray of modifiedMessages) {

  // logger.log(`[CosmosDB:Insert] messageArray: ${JSON.stringify(messageArray)}`);
  
    for (const message of messageArray) {

      // logger.log(`[CosmosDB:Insert] message123: ${JSON.stringify(message)}`);
  
      if (!message.id) {
        logger.log(`[CosmosDB:Process] Error: message.id is undefined for message: ${JSON.stringify(message.id)}`);
        continue;
      }

      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: message.id }]
      };

      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();
        if (existingMessages.length === 0) {
          await container.items.create(message);
          insertedCount++;
        }
      } catch (error) {
        logger.log(`[CosmosDB:Process] Error processing message: ${error}`);
      }
    }
  }

  return insertedCount;
}

export async function deleteOldMessagesFromCosmosDB(
  logger: CustomLogger,
  filterDate: string
): Promise<void> {

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Delete] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const encodedDate = encodeURIComponent(filterDate);
    logger.log(`[CosmosDB:Delete] Date: ${encodedDate}`);

    const container = client.database(databaseName).container(containerName);

    const querySpec = {
      query: "SELECT * FROM c WHERE c.receivedDateTime <= @filterDate ORDER BY c.receivedDateTime DESC",
      parameters: [{ name: "@filterDate", value: filterDate }]
    };

    const { resources: oldMessages } = await container.items.query(querySpec).fetchAll();
    logger.log(`Found ${oldMessages.length} TeamsChat to Delete`);

    oldMessages.forEach(message => {
      // logger.log(`[CosmosDB:Delete] Message-ID to delete: ${message.id}`);
      logger.log(`[CosmosDB:Delete] Message receivedDateTime: ${message.receivedDateTime}`);
    });

    // const containerDefinition = await container.read();
    // logger.log("Partition Key Path:", containerDefinition.resource?.partitionKey?.paths[0]);

    let deletedCount = 0;

    for (const message of oldMessages) {
      try {
        await container.item(message.id, message.id).delete();
        deletedCount++;
        logger.log(`[CosmosDB:Delete] Successfully Deleted Message-ID: ${message.id}`);
      } catch (error) {
        logger.log(`[CosmosDB:Delete] Error Deleting Message-ID: ${message.id}: ${error}`);
      }
    }

    logger.log(`[CosmosDB:Delete] Deleted ${deletedCount} TeamsChat from Cosmos DB`);

    const { resources: [count] } = await container.items
      .query("SELECT VALUE COUNT(1) FROM c")
      .fetchAll();
    logger.log(`[CosmosDB:Delete] Container has: ${count} Items`);

  } catch (error) {
    logger.log(`[CosmosDB:Delete] Error TeamsChat: ${error}`);
    throw error;
  }
}

export async function deleteAllDocumentsFromCosmosDB(logger: CustomLogger): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Delete] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const database = client.database(databaseName);
    const container = database.container(containerName);

    logger.log(`[CosmosDB:Delete] Deleting Entire Container: ${containerName}`);

    await container.delete();
    await database.containers.createIfNotExists({ id: containerName });

    logger.log(`[CosmosDB:Delete] All Data Deleted. Container Recreated.`);
  } catch (error) {
    logger.log(`[CosmosDB:Delete] Error: ${error}`);
    throw error;
  }
}