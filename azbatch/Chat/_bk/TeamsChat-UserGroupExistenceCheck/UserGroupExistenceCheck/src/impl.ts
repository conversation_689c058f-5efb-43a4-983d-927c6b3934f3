import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';

import { createUserTeamsChatRequests, createUserTeamsChatMessagesRequests } from '../../utilities/userTeamsChat';
import { splitArrayIntoChunks } from '../../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForTeamsChat
} from '../../utilities/graph';
import { logLongArray, CustomLogger } from '../../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  ITeamsChatMessageEvent
} from '../../utilities/models';
import {
  removeDeletedUsersFromSecurityField
} from '../../utilities/cosmos';

// GraphAPIのバッチリクエスト最大数
const MAX_GRAPH_API_USER_TEAMSCHAT_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_USER_TEAMSCHAT_BATCH_COUNTS ?? '5');
const MAX_GRAPH_API_USER_TEAMSCHATMESSAGES_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_USER_TEAMSCHATMESSAGES_BATCH_COUNTS ?? '5');
const MAX_TEAMSCHATMESSAGES_CHUNK_SIZE = parseInt(process.env.MAX_TEAMSCHATMESSAGES_CHUNK_SIZE ?? '20');

type Logger = CustomLogger;

/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}

/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  logger: Logger,
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);
  logLongArray(logger, '[Impl:fetchTargetUsers] AllGroupUsers.id', 800, users.map((u) => u.id));

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}

/**********************************************************************************************************************************************************************/

/* Send request to `/users/${userId}/chats` API per user */
export async function processUserTeamsChat(
  logger: Logger,
  client: Client,
  usersData: IBatchResponseData[],
): Promise<void> {

  logger.log(`\n[Impl:processUserTeamsChat] Total UsersData to Process: ${usersData.length}`);

  // create the chat request for each user
  const userTeamsChatBatchRequestsCreated: BatchRequestData[] = usersData
    .filter(userElements => userElements.id)
    .flatMap((userElements) =>
      createUserTeamsChatRequests(userElements?.id ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processUserTeamsChat] Total GraphAPI Created TeamsChatRequests: ${userTeamsChatBatchRequestsCreated.length} | MAX_GRAPH_API_USER_TEAMSCHAT_BATCH_COUNTS: ${MAX_GRAPH_API_USER_TEAMSCHAT_BATCH_COUNTS}`);
  const userSplitTeamsChatBatchRequests = splitArrayIntoChunks(userTeamsChatBatchRequestsCreated, MAX_GRAPH_API_USER_TEAMSCHAT_BATCH_COUNTS);
  logger.log(`[Impl:processUserTeamsChat] Total TeamsChat Split Batch: ${userSplitTeamsChatBatchRequests.length}`);
  // logger.log(`[Impl:processUserTeamsChat] userSplitTeamsChatBatchRequests == ${JSON.stringify(userSplitTeamsChatBatchRequests)}\n`);

  await processUserTeamsChatBatchRequests(logger, client, userSplitTeamsChatBatchRequests);

  userSplitTeamsChatBatchRequests.length = 0;
}

async function processUserTeamsChatBatchRequests(
  logger: Logger,
  client: Client,
  userSplitTeamsChatBatchRequests: BatchRequestData[][]
): Promise<void> {

  const totalTeamsChatBatchRequests = userSplitTeamsChatBatchRequests.length;

  // Process one batch at a time (each containing max of 20 user requests)
  for (let i = 0; i < totalTeamsChatBatchRequests; i++) {
    try {
      const currentTeamsChatBatchRequests = i + 1;

      logger.log(
        `\n====== BATCH START - TEAMS_CHAT: (currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}) ======`
      );

      const currentUserSplitTeamsChatBatchRequests = userSplitTeamsChatBatchRequests[i];
      logger.log(`[Impl:processUserTeamsChatBatchRequests] currentUserSplitTeamsChatBatchRequests == ${JSON.stringify(currentUserSplitTeamsChatBatchRequests)} === currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

      // Process 1 batch request at a time
      const batchResultTeamsChat = await fetchJsonBatchForTeamsChat(logger, client, currentUserSplitTeamsChatBatchRequests);
      // *** View Raw Response
      logger.log(`[Impl:processUserTeamsChatBatchRequests] *** batchResultTeamsChat == ${JSON.stringify(batchResultTeamsChat)} === currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`
      );

      if (!batchResultTeamsChat.responses || batchResultTeamsChat.responses.length === 0) {
        logger.log(`[Impl:processUserTeamsChatBatchRequests] No Responses in Batch === currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);
        continue;
      }

      processUserTeamsChatMessagesBatchRequest(logger, client, batchResultTeamsChat, currentTeamsChatBatchRequests, totalTeamsChatBatchRequests);

      logger.log( `\n====== BATCH END - TEAMS_CHAT: (currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests} - COMPLETE) ======\n`);

    } catch (error) {
      logger.error(`[Impl:processUserTeamsChatBatchRequests] Error processing batch starting at index ${i}: ${error}`);
      continue;
    }

    global.gc && global.gc();
  }
}

/**********************************************************************************************************************************************************************/

/* Send request to `/users/${userId}/chats{chatId}/messages` API per user */
export async function processUserTeamsChatMessagesBatchRequest(
  logger: Logger,
  client: Client,
  batchResultTeamsChat: IBatchResponses,
  currentTeamsChatBatchRequests: number,
  totalTeamsChatBatchRequests: number,
): Promise<void> {

  logger.log(`\n[Impl:processUserTeamsChatMessagesBatchRequest] Total batchResultTeamsChat to Process: ${batchResultTeamsChat.responses?.length ?? 0} === currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

  // create the chat messages request for each user chats
  const userTeamsChatMessagesBatchRequestsCreated: BatchRequestData[] = (batchResultTeamsChat.responses ?? [])
    .filter(response => response.id && response.status === 200)
    // // Sort responses by week number extracted from the id
    // .sort((a, b) => {
    //   const weekA = parseInt(a.id?.split('_week')[1] ?? '0', 10); // Extract week number from id
    //   const weekB = parseInt(b.id?.split('_week')[1] ?? '0', 10); // Extract week number from id
    //   return weekA - weekB; // Sort in ascending order
    // })
    // Sort first by user ID (part before "_week"), then by week number
    .sort((a, b) => {
      // Extract user ID (everything before _week)
      const userIdA = a.id?.split('_week')[0] || '';
      const userIdB = b.id?.split('_week')[0] || '';
      
      // If user IDs are different, sort by user ID
      if (userIdA !== userIdB) {
        return userIdA.localeCompare(userIdB);
      }
      
      // If same user ID, sort by week number
      const weekA = parseInt(a.id?.split('_week')[1] || '0', 10);
      const weekB = parseInt(b.id?.split('_week')[1] || '0', 10);
      return weekA - weekB;
    })
    .flatMap(response => {
      // const userId = response.id?.split('_week')[0] ?? ''; // Extract everything before "_week_"
      const userId = response.id ?? '';
      const chatsData = response.body?.value || [];
      return chatsData
        .filter(chatElements => chatElements.id)
        .flatMap(chatElements => 
          createUserTeamsChatMessagesRequests(userId ?? '', chatElements.id ?? '').map(request => ({
            id: request.id,
            method: request.method,
            url: request.url
          }))
        );
    });

  logger.log(`[Impl:processUserTeamsChatMessagesBatchRequest] Total Created GraphAPI TeamsChatMessagesRequests: ${userTeamsChatMessagesBatchRequestsCreated.length} | MAX_GRAPH_API_USER_TEAMSCHATMESSAGES_BATCH_COUNTS: ${MAX_GRAPH_API_USER_TEAMSCHATMESSAGES_BATCH_COUNTS} === currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);
  const userSplitTeamsChatMessagesBatchRequests = splitArrayIntoChunks(userTeamsChatMessagesBatchRequestsCreated, MAX_GRAPH_API_USER_TEAMSCHATMESSAGES_BATCH_COUNTS);
  logger.log(`[Impl:processUserTeamsChatMessagesBatchRequest] Total TeamsChatMessages Split Batch: ${userSplitTeamsChatMessagesBatchRequests.length} === currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);
  // logger.log(`[Impl:processUserTeamsChatMessagesBatchRequest] userSplitTeamsChatMessagesBatchRequests == ${JSON.stringify(userSplitTeamsChatMessagesBatchRequests)} === currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}\n`);

  await processUserTeamsChatMessagesBatchRequests(logger, client, userSplitTeamsChatMessagesBatchRequests, currentTeamsChatBatchRequests, totalTeamsChatBatchRequests);

  userSplitTeamsChatMessagesBatchRequests.length = 0;
}

async function processUserTeamsChatMessagesBatchRequests(
  logger: Logger,
  client: Client,
  userSplitTeamsChatMessagesBatchRequests: BatchRequestData[][],
  currentTeamsChatBatchRequests: number,
  totalTeamsChatBatchRequests: number
): Promise<void> {

  const totalTeamsChatMessagesBatchRequests = userSplitTeamsChatMessagesBatchRequests.length;

  // Process one batch at a time (each containing max of 50 user requests)
  for (let i = 0; i < totalTeamsChatMessagesBatchRequests; i++) {
    try {
      const currentTeamsChatMessagesBatchRequests = i + 1;

      logger.log(`\n====== BATCH START - TEAMS_CHAT_MESSAGES: (currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}) ======`);

      const currentUserSplitTeamsChatMessagesBatchRequests = userSplitTeamsChatMessagesBatchRequests[i];
      logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] currentUserSplitTeamsChatMessagesBatchRequests == ${JSON.stringify(currentUserSplitTeamsChatMessagesBatchRequests)} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

      // Process 1 batch request at a time
      const batchResultTeamsChatMessages = await fetchJsonBatchForTeamsChat(logger, client, currentUserSplitTeamsChatMessagesBatchRequests);
      // *** View Raw Response
      logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] *** batchResultTeamsChatMessages == ${JSON.stringify(batchResultTeamsChatMessages)} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

      if (!batchResultTeamsChatMessages.responses || batchResultTeamsChatMessages.responses.length === 0) {
        logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] No Responses in Batch === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}}`);
        continue;
      }

      // Process all user responses in the batch
      await processBatchResultTeamsChatMessages(logger, batchResultTeamsChatMessages, currentTeamsChatMessagesBatchRequests, totalTeamsChatMessagesBatchRequests, currentTeamsChatBatchRequests, totalTeamsChatBatchRequests);


// // Process 1 batch request at a time
// const batchResultTeamsChatMessages = await fetchJsonBatchForTeamsChat(logger, client, currentUserSplitTeamsChatMessagesBatchRequests);
// // *** View Raw Response
// logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] *** batchResultTeamsChatMessages == ${JSON.stringify(batchResultTeamsChatMessages)} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

// if (!batchResultTeamsChatMessages.responses || batchResultTeamsChatMessages.responses.length === 0) {
//   logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] No Responses in Batch === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}}`);
//   continue;
// }

// // Log the order of IDs in the original responses
// if (batchResultTeamsChatMessages.responses) {
//   const originalIds = batchResultTeamsChatMessages.responses.map(response => response.id);
//   logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] ORIGINAL RESPONSE ORDER: ${JSON.stringify(originalIds)}`);
// }

// // Create a sorted copy of the responses
// const sortedBatchResultTeamsChatMessages = {
//   ...batchResultTeamsChatMessages,
//   responses: [...(batchResultTeamsChatMessages.responses || [])]
// };

// // Sort the copy by user ID first, then by week number
// if (sortedBatchResultTeamsChatMessages.responses) {
//   sortedBatchResultTeamsChatMessages.responses.sort((a, b) => {
//     // Extract user ID (everything before _week)
//     const userIdA = a.id?.split('_week')[0] || '';
//     const userIdB = b.id?.split('_week')[0] || '';
    
//     // If user IDs are different, sort by user ID
//     if (userIdA !== userIdB) {
//       return userIdA.localeCompare(userIdB);
//     }
    
//     // If same user ID, sort by week number
//     const weekA = parseInt(a.id?.split('_week')[1]?.split('_')[0] || '0', 10);
//     const weekB = parseInt(b.id?.split('_week')[1]?.split('_')[0] || '0', 10);
//     return weekA - weekB;
//   });
  
//   // Log the order of IDs in the sorted responses
//   const sortedIds = sortedBatchResultTeamsChatMessages.responses.map(response => response.id);
//   logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] SORTED RESPONSE ORDER: ${JSON.stringify(sortedIds)}`);
// }

// // Process all user responses in the sorted batch
// await processBatchResultTeamsChatMessages(logger, sortedBatchResultTeamsChatMessages, currentTeamsChatMessagesBatchRequests, totalTeamsChatMessagesBatchRequests, currentTeamsChatBatchRequests, totalTeamsChatBatchRequests);
     

      // const currentUserSplitTeamsChatMessagesBatchRequests = userSplitTeamsChatMessagesBatchRequests[i];
      // logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] currentUserSplitTeamsChatMessagesBatchRequests == ${JSON.stringify(currentUserSplitTeamsChatMessagesBatchRequests)} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

      // // Process 1 batch request at a time
      // const batchResultTeamsChatMessages = await fetchJsonBatchForTeamsChat(logger, client, currentUserSplitTeamsChatMessagesBatchRequests);

      // // Create a copy with a properly ordered responses array
      // const orderedBatchResultTeamsChatMessages: IBatchResponses = {
      //   ...batchResultTeamsChatMessages,
      //   responses: []
      // };

      // // Map responses by their IDs for easy lookup
      // const responseMap: { [key: string]: IBatchResponseData } = {};
      // if (batchResultTeamsChatMessages.responses) {
      //   batchResultTeamsChatMessages.responses.forEach(response => {
      //     if (response.id) {
      //       responseMap[response.id] = response;
      //     }
      //   });
      // }

      // // Add responses in the same order as the requests
      // if (currentUserSplitTeamsChatMessagesBatchRequests.length > 0) {
      //   orderedBatchResultTeamsChatMessages.responses = currentUserSplitTeamsChatMessagesBatchRequests
      //     .map(request => responseMap[request.id])
      //     .filter(response => response !== undefined);
      // }

      // // Log the ordered responses for verification
      // logger.log(`[Impl:processUserTeamsChatMessagesBatchRequests] ORDERED RESPONSE IDS: ${JSON.stringify((orderedBatchResultTeamsChatMessages.responses ?? []).map(r => r.id))}`);


      // // Process the ordered responses
      // await processBatchResultTeamsChatMessages(
      //   logger, 
      //   orderedBatchResultTeamsChatMessages,
      //   currentTeamsChatMessagesBatchRequests, 
      //   totalTeamsChatMessagesBatchRequests, 
      //   currentTeamsChatBatchRequests, 
      //   totalTeamsChatBatchRequests
      // );

      // Clear batch responses AFTER processing ALL users
      batchResultTeamsChatMessages.responses = [];

      // Free memory for this batch
      userSplitTeamsChatMessagesBatchRequests[i] = [];

      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`\n====== BATCH END - TEAMS_CHAT_MESSAGES: (currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}) ======\n`);

    } catch (error) {
      logger.error(`[Impl:processUserTeamsChatMessagesBatchRequests] Error processing batch starting at index ${i}: ${error}`);
      continue;
    }

    global.gc && global.gc();
  }
}

/**********************************************************************************************************************************************************************/

async function processBatchResultTeamsChatMessages(
  logger: Logger,
  batchResultTeamsChatMessages: IBatchResponses,
  currentTeamsChatMessagesBatchRequests: number,
  totalTeamsChatMessagesBatchRequests: number,
  currentTeamsChatBatchRequests: number,
  totalTeamsChatBatchRequests: number
): Promise<void> {

  if (!batchResultTeamsChatMessages.responses || batchResultTeamsChatMessages.responses.length === 0) {
    return;
  }

  const totalBatchResultTeamsChatMessages = batchResultTeamsChatMessages.responses.length;

  for (let j = 0; j < totalBatchResultTeamsChatMessages; j++) {
    const currentBatchResultTeamsChatMessages = j + 1;
    const batchResultTeamsChatMessagesResponses = batchResultTeamsChatMessages.responses[j];

    // Skip if responses is undefined or status is not 200
    if (!batchResultTeamsChatMessagesResponses || batchResultTeamsChatMessagesResponses.status !== 200) {
      const batchResultTeamsChatMessagesResponses_id = batchResultTeamsChatMessagesResponses?.id ?? 'User-ID-undefined';
      logger.log(`[Impl:processBatchResultTeamsChatMessages] Skipping User-ID: ${batchResultTeamsChatMessagesResponses_id} with Error Data: ${batchResultTeamsChatMessagesResponses?.status} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);
      continue;
    }

    const userId = batchResultTeamsChatMessagesResponses.id;

    logger.log(`\n\n+=+= PROCESSING BATCH START: (batchResultTeamsChatMessages Responses: ${currentBatchResultTeamsChatMessages} of ${totalBatchResultTeamsChatMessages} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}) +=+=`);
    logger.log(`+=+= PROCESSING BATCH START: (User-ID: ${userId} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}) +=+=`);

    const singleTeamsChatMessagesResult = { responses: [batchResultTeamsChatMessagesResponses] };
    logger.info(`[Impl:processBatchResultTeamsChatMessages] singleTeamsChatMessagesResult == ${JSON.stringify(singleTeamsChatMessagesResult)} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

    const modifiedUserTeamsChatMessagesChunk = processSingleUserTeamsChatMessagesResult([singleTeamsChatMessagesResult], logger);
    // *** View Raw Response
    logger.info(`[Impl:processBatchResultTeamsChatMessages] *** modifiedUserTeamsChatMessagesChunk == ${JSON.stringify(modifiedUserTeamsChatMessagesChunk)} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);

    const totalModifiedUserTeamsChatMessagesChunk = modifiedUserTeamsChatMessagesChunk.length;

    // Insert each chunk
    for (let k = 0; k < totalModifiedUserTeamsChatMessagesChunk; k++) {
      const chunk = modifiedUserTeamsChatMessagesChunk[k];
      const currentModifiedUserTeamsChatMessagesChunk = k + 1;

      if (!chunk) {
        logger.info(`[Impl:processBatchResultTeamsChatMessages] Skipping Undefined TeamsChatMessagesChunk at Index: ${k} for User-ID: ${userId} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}`);
        continue;
      }

      logger.log(`\n--- INSERT COSMOS_DB START: (currentBatchResultTeamsChatMessages Responses: ${currentBatchResultTeamsChatMessages} of totalBatchResultTeamsChatMessages: ${totalBatchResultTeamsChatMessages}) === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests} ---`);
      logger.log(`--- INSERT COSMOS_DB START: (currentModifiedUserTeamsChatMessagesChunk Responses: ${currentModifiedUserTeamsChatMessagesChunk} of totalModifiedUserTeamsChatMessagesChunk: ${totalModifiedUserTeamsChatMessagesChunk} with ${chunk.body?.value?.length} TeamsChat Inside) === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests} ---`);

      // try {
      //   logger.info(`[Impl:processBatchResultTeamsChatMessages] Inserting TeamsChatGroupMessages...`);
      //   await removeDeletedUsersFromSecurityField(logger, [chunk]);
      //   logger.info(`[Impl:processBatchResultTeamsChatMessages] Successfully Inserted TeamsChatGroupMessages`);

      // } catch (error) {
      //   logger.error(`[Impl:processBatchResultTeamsChatMessages] Failed Inserting: (currentModifiedUserTeamsChatMessagesChunk Responses: ${currentModifiedUserTeamsChatMessagesChunk}): ${error}`);
      //   continue;
      // }

      modifiedUserTeamsChatMessagesChunk[k] = {} as IBatchResponseData;

      logger.log(`--- INSERT COSMOS_DB END: (batchResultTeamsChatMessages Responses: ${currentBatchResultTeamsChatMessages} of ${totalBatchResultTeamsChatMessages}) === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests} ---`);
      logger.log(`--- INSERT COSMOS_DB END: (currentModifiedUserTeamsChatMessagesChunk Responses: ${currentModifiedUserTeamsChatMessagesChunk} of ${totalModifiedUserTeamsChatMessagesChunk}) === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}\n---`);
    }

    // Clear array
    modifiedUserTeamsChatMessagesChunk.length = 0;

    // Give GC a chance to run
    global.gc && global.gc();

    // Small pause
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`+=+= PROCESSING BATCH END: (User-ID: ${userId} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}) +=+=`);
    logger.log(`+=+= PROCESSING BATCH END: (batchResultTeamsChatMessages Responses: ${currentBatchResultTeamsChatMessages} of ${batchResultTeamsChatMessages.responses.length} === currentTeamsChatMessagesBatchRequests: ${currentTeamsChatMessagesBatchRequests} of totalTeamsChatMessagesBatchRequests: ${totalTeamsChatMessagesBatchRequests} | currentTeamsChatBatchRequests: ${currentTeamsChatBatchRequests} of totalTeamsChatBatchRequests: ${totalTeamsChatBatchRequests}) +=+=\n\n`);
  }
}

function processSingleUserTeamsChatMessagesResult(singleTeamsChatMessagesResult: IBatchResponses[], logger: Logger): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleTeamsChatMessagesResult) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleUserTeamsChatMessagesResult] Skipping Invalid TeamsChatMessages Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      // Skip invalid responses
      if (response.status !== 200 || !response.body?.value || !Array.isArray(response.body.value)) {
        if (response?.body?.error?.innerError) {
          logger.log(`[Impl:processSingleUserTeamsChatMessagesResult] Error Response == ${JSON.stringify(response)}`);
        }
        continue;
      }

      // const userId = response.id?.split('_chat_')[0] ?? ''; // Extract everything before "_chat_"
      const userId = response.id?.split('_week')[0] ?? ''; // Extract everything before "_week"
      // const userId = response.id ?? ''; 

      const totalTeamsChatMessages = response.body?.value?.length || 0; // Count the total messages in response.body.value
      logger.log(`\n[Impl:processSingleUserTeamsChatMessagesResult] Total TeamsChatMessages in response.body.value: ${totalTeamsChatMessages}`);

      // Filter only the messages with membersDeletedEventMessageDetail and membersAddedEventMessageDetail
      const filteredTeamsChatMessages = (response.body.value as ITeamsChatMessageEvent[])
      .filter((item: ITeamsChatMessageEvent) => {
        return !!(item.eventDetail && 
          (item.eventDetail["@odata.type"] === "#microsoft.graph.membersDeletedEventMessageDetail" ||
           item.eventDetail["@odata.type"] === "#microsoft.graph.membersAddedEventMessageDetail"));
      })
      .sort((a, b) => {
        const dateA = new Date(a.lastModifiedDateTime || 0);
        const dateB = new Date(b.lastModifiedDateTime || 0);
        return dateA.getTime() - dateB.getTime(); // Ascending order (oldest first)
      });

      const chunkSize = MAX_TEAMSCHATMESSAGES_CHUNK_SIZE;
      // logger.log(`[Impl:processSingleUserTeamsChatMessagesResult] Original Response == ${JSON.stringify(response)}`);

      // Process all teams chat of the user by chunks
      processUserTeamsChatGroupMessagesByChunk(userId, filteredTeamsChatMessages, allProcessedData, chunkSize, logger);

      filteredTeamsChatMessages.length = 0;
    }
  }

  // logger.info(`[Impl:processSingleUserTeamsChatMessagesResult] allProcessedData == ${JSON.stringify(allProcessedData)}`);
  return allProcessedData;
}

function processUserTeamsChatGroupMessagesByChunk(
  userId: string,
  allTeamsChatGroupMessages: ITeamsChatMessageEvent[],
  allProcessedData: IBatchResponseData[],
  chunkSize: number,
  logger: Logger
): void {

  for (let i = 0; i < allTeamsChatGroupMessages.length; i += chunkSize) {
    const teamsChatAfterChunk = allTeamsChatGroupMessages.slice(i, i + chunkSize);

    processTeamsChatGroupMessagesChunk(userId, teamsChatAfterChunk, allProcessedData, logger);

    teamsChatAfterChunk.length = 0;
  }
}

function processTeamsChatGroupMessagesChunk(
  userId: string,
  teamsChatAfterChunk: ITeamsChatMessageEvent[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues = [];

  for (const item of teamsChatAfterChunk) {
    processedValues.push(modifyTeamsChatGroupMessagesDetails(item));
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: userId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processTeamsChatGroupMessagesChunk] Successfully Modified TeamsChatMessages: ${processedValues.length} TeamsChatMessages Inside Chunk | MAX_TEAMSCHATMESSAGES_CHUNK_SIZE: ${MAX_TEAMSCHATMESSAGES_CHUNK_SIZE}\n`);
  }
}

function modifyTeamsChatGroupMessagesDetails(item: ITeamsChatMessageEvent) {
  return {
    id: item.id,
    chatId: item.chatId,
    lastModifiedDateTime: item.lastModifiedDateTime,
    body: {
      content: item.body.content
    },
    eventDetail: item.eventDetail ? {
      "@odata.type": item.eventDetail["@odata.type"],
      visibleHistoryStartDateTime: item.eventDetail.visibleHistoryStartDateTime,
      members: item.eventDetail.members
    } : null
  };
}