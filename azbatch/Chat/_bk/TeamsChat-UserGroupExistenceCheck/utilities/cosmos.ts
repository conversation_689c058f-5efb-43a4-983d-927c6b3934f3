import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeamsChatMessageEvent } from "./models";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:Client] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:Client] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBK<PERSON>,
      });
      logger.log("[CosmosDB:Client] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:Client] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:Client] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Validate] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:Validate] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:Validate] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:Validate] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:Validate] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:Validate] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:Validate] Error Connection: ${error}`);
    throw error;
  }
}

export async function removeDeletedUsersFromSecurityField(
  logger: CustomLogger,
  userMessages: IBatchResponseData[]
): Promise<void> {

  logger.log(`[CosmosDB:Insert] userMessages: ${JSON.stringify(userMessages)}`);

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Delete] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    // Extract the messages with deleted members
    const messagesWithDeletedMembers: ITeamsChatMessageEvent[][] = userMessages
      .map((message) => message.body?.value || [])
      .filter(Array.isArray);
    
    const { removedCount, addedCount } = await processSystemMessages(container, messagesWithDeletedMembers, logger);

    logger.log(`[CosmosDB:Process] Removed ${removedCount} user IDs from security_user_id fields`);
    logger.log(`[CosmosDB:Process] Added ${addedCount} user IDs to security_user_id fields`);
  } catch (error) {
    logger.log(`[CosmosDB:Delete] Error processing system messages: ${error}`);
    throw error;
  }
}

async function processSystemMessages(
  container: Container,
  messages: ITeamsChatMessageEvent[][],
  logger: CustomLogger
): Promise<{ removedCount: number, addedCount: number }> {
  let removedCount = 0;
  let addedCount = 0;

  logger.log(`[CosmosDB:Process] Processing system messages: ${messages.length} arrays`);

  for (const messageArray of messages) {
    // Messages are already sorted chronologically in index.ts
    for (const message of messageArray) {
      if (!message.id || !message.eventDetail) {
        logger.log(`[CosmosDB:Process] Skipping message with no ID or event details: ${JSON.stringify(message.id)}`);
        continue;
      }

      const eventType = message.eventDetail["@odata.type"];
      const timestamp = message.lastModifiedDateTime || new Date().toISOString();
      
      if (eventType === "#microsoft.graph.membersDeletedEventMessageDetail") {
        // Handle deletion events
        const removed = await processDeletedMembers(container, message, timestamp, logger);
        removedCount += removed;
      } 
      else if (eventType === "#microsoft.graph.membersAddedEventMessageDetail") {
        // Handle addition events
        const added = await processAddedMembers(container, message, timestamp, logger);
        addedCount += added;
      }
      else {
        logger.log(`[CosmosDB:Process] Skipping message with unknown event type: ${eventType}`);
        continue;
      }
    }
  }
  
  return { removedCount, addedCount };
}

async function processDeletedMembers(
  container: Container, 
  message: ITeamsChatMessageEvent, 
  deletionTimestamp: string, 
  logger: CustomLogger
): Promise<number> {
  let removed = 0;
  // Extract the IDs of deleted users
  const deletedUserIds = message.eventDetail?.members?.map(member => member.id) || [];
  
  logger.log(`[CosmosDB:Process] Processing chatId ${message.chatId} with deleted members: ${JSON.stringify(deletedUserIds)}`);
  
  if (deletedUserIds.length === 0) {
    logger.log(`[CosmosDB:Process] No deleted user IDs found in message: ${message.id}`);
    return removed;
  }

  const basicQuerySpec = {
    query: "SELECT * FROM c WHERE c.chatId = @chatId",
    parameters: [{ name: "@chatId", value: message.chatId }],
  };

  try {
    const { resources: matchingMessages } = await container.items
      .query(basicQuerySpec)
      .fetchAll();

    if (matchingMessages.length === 0) {
      logger.log(`[CosmosDB:Process] No messages found with chatId: ${message.chatId}`);
      return removed;
    }
    
    logger.log(`[CosmosDB:Process] Found ${matchingMessages.length} messages with chatId: ${message.chatId}`);
    
    // Process each matching message
    for (const existingMsg of matchingMessages) {
      const existingUserIds = existingMsg.security_user_id || [];
      
      // Find which user IDs will be removed
      const userIdsToRemove = existingUserIds.filter((id: string) => deletedUserIds.includes(id));
      
      if (userIdsToRemove.length === 0) {
        logger.log(`[CosmosDB:Process] No matching user IDs to remove from message: ${existingMsg.id}`);
        continue;
      }
      
      // Filter out the deleted user IDs from the security_user_id array
      const updatedUserIds = existingUserIds.filter((id: string) => !deletedUserIds.includes(id));

      // Only update if there's a change in the array AND if the user is still present
      // This prevents redundant operations for already processed messages
      if (updatedUserIds.length < existingUserIds.length) {
        await container.item(existingMsg.id, existingMsg.id).replace({
          ...existingMsg,
          security_user_id: updatedUserIds,
        });
        
        const removedUsers = existingUserIds.length - updatedUserIds.length;
        removed += removedUsers;
        
        logger.log(`[CosmosDB:Process] Message ${existingMsg.id}: Removed ${removedUsers} user IDs: ${JSON.stringify(userIdsToRemove)}`);
      } else {
        logger.log(`[CosmosDB:Process] No user IDs removed from message: ${existingMsg.id}`);
      }
    }
  } catch (error) {
    logger.log(`[CosmosDB:Process] Error processing deleted users for chatId ${message.chatId}: ${error}`);
  }
  
  return removed;
}

// Function to handle member addition events
async function processAddedMembers(
  container: Container, 
  message: ITeamsChatMessageEvent, 
  additionTimestamp: string, 
  logger: CustomLogger
): Promise<number> {
  let added = 0;
  // Extract the IDs of added users
  const addedUserIds = message.eventDetail?.members?.map(member => member.id) || [];
  const visibleHistoryStartDateTime = message.eventDetail?.visibleHistoryStartDateTime || null;
  
  logger.log(`[CosmosDB:Process] Processing chatId ${message.chatId} with added members: ${JSON.stringify(addedUserIds)}`);
  
  if (addedUserIds.length === 0) {
    logger.log(`[CosmosDB:Process] No added user IDs found in message: ${message.id}`);
    return added;
  }

  // Determine if we should include all history for added users
  const includeAllHistory = !visibleHistoryStartDateTime;
  
  try {
    if (includeAllHistory) {
      // If including all history, add users to all messages in the chat
      const allMessagesQuery = {
        query: "SELECT * FROM c WHERE c.chatId = @chatId",
        parameters: [{ name: "@chatId", value: message.chatId }],
      };
      
      const { resources: allMessages } = await container.items
        .query(allMessagesQuery)
        .fetchAll();
        
      logger.log(`[CosmosDB:Process] Including all history: Found ${allMessages.length} messages for chatId: ${message.chatId}`);
      
      // Update each message to include the added users
      for (const existingMsg of allMessages) {
        const existingUserIds = existingMsg.security_user_id || [];
        
        // Only add users that aren't already in the security_user_id array
        const newUserIds = addedUserIds.filter(id => !existingUserIds.includes(id));
        
        // Skip if all user IDs are already present (already processed by another user's event)
        if (newUserIds.length > 0) {
          const updatedUserIds = [...existingUserIds, ...newUserIds];
          
          await container.item(existingMsg.id, existingMsg.id).replace({
            ...existingMsg,
            security_user_id: updatedUserIds,
          });
          
          added += newUserIds.length;
          
          logger.log(`[CosmosDB:Process] Message ${existingMsg.id}: Added ${newUserIds.length} user IDs: ${JSON.stringify(newUserIds)}`);
        } else {
          logger.log(`[CosmosDB:Process] Message ${existingMsg.id}: All users already present, skipping update`);
        }
      }
    } else {
      // If not including all history, only add users to messages after visibleHistoryStartDateTime
      const newMessagesQuery = {
        query: "SELECT * FROM c WHERE c.chatId = @chatId AND c.createdDateTime >= @visibleFrom",
        parameters: [
          { name: "@chatId", value: message.chatId },
          { name: "@visibleFrom", value: visibleHistoryStartDateTime }
        ],
      };
      
      const { resources: newMessages } = await container.items
        .query(newMessagesQuery)
        .fetchAll();
        
      logger.log(`[CosmosDB:Process] Limited history: Found ${newMessages.length} messages after ${visibleHistoryStartDateTime}`);
      
      // Update only the newer messages
      for (const existingMsg of newMessages) {
        const existingUserIds = existingMsg.security_user_id || [];
        
        // Only add users that aren't already in the security_user_id array
        const newUserIds = addedUserIds.filter(id => !existingUserIds.includes(id));
        
        // Skip if all user IDs are already present (already processed by another user's event)
        if (newUserIds.length > 0) {
          const updatedUserIds = [...existingUserIds, ...newUserIds];
          
          await container.item(existingMsg.id, existingMsg.id).replace({
            ...existingMsg,
            security_user_id: updatedUserIds,
          });
          
          added += newUserIds.length;
          
          logger.log(`[CosmosDB:Process] Message ${existingMsg.id}: Added ${newUserIds.length} user IDs: ${JSON.stringify(newUserIds)}`);
        } else {
          logger.log(`[CosmosDB:Process] Message ${existingMsg.id}: All users already present, skipping update`);
        }
      }
    }
  } catch (error) {
    logger.log(`[CosmosDB:Process] Error processing added users for chatId ${message.chatId}: ${error}`);
  }
  
  return added;
}


// async function processDeletedUserMessages(
//   container: Container,
//   messagesWithDeletedMembers: ITeamsChatMessageEvent[][],
//   logger: CustomLogger
// ): Promise<number> {
//   let removedCount = 0;

//   logger.log(`[CosmosDB:Process] Processing deleted members events: ${messagesWithDeletedMembers.length} arrays`);

//   for (const messageArray of messagesWithDeletedMembers) {
//     for (const message of messageArray) {
//       if (!message.id || !message.eventDetail?.members) {
//         logger.log(`[CosmosDB:Process] Skipping message with no ID or members: ${JSON.stringify(message.id)}`);
//         continue;
//       }

//       // Extract the IDs of deleted users and the message timestamp
//       const deletedUserIds = message.eventDetail.members.map(member => member.id);
//       const deletionTimestamp = message.lastModifiedDateTime || new Date().toISOString();
      
//       logger.log(`[CosmosDB:Process] Processing chatId ${message.chatId} with deleted members: ${JSON.stringify(deletedUserIds)}`);
//       logger.log(`[CosmosDB:Process] Deletion timestamp: ${deletionTimestamp}`);
      
//       if (deletedUserIds.length === 0) {
//         logger.log(`[CosmosDB:Process] No deleted user IDs found in message: ${message.id}`);
//         continue;
//       }

//       const basicQuerySpec = {
//         query: "SELECT * FROM c WHERE c.chatId = @chatId",
//         parameters: [{ name: "@chatId", value: message.chatId }],
//       };

//       try {
//         const { resources: matchingMessages } = await container.items
//           .query(basicQuerySpec)
//           .fetchAll();

//         if (matchingMessages.length === 0) {
//           logger.log(`[CosmosDB:Process] No messages found with chatId: ${message.chatId}`);
//           continue;
//         }
        
//         logger.log(`[CosmosDB:Process] Found ${matchingMessages.length} messages with chatId: ${message.chatId}`);
        
//         // Process each matching message
//         for (const existingMsg of matchingMessages) {
//           const existingUserIds = existingMsg.security_user_id || [];
          
//           // Find which user IDs will be removed
//           const userIdsToRemove = existingUserIds.filter((id: string) => deletedUserIds.includes(id));
          
//           if (userIdsToRemove.length === 0) {
//             logger.log(`[CosmosDB:Process] No matching user IDs to remove from message: ${existingMsg.id}`);
//             continue;
//           }
          
//           // Check for each user if they were re-added after this deletion event
//           const updatedUserIds = [];
//           const skippedUserIds = [];

//           for (const userId of existingUserIds) {
//             // Skip users that aren't in the deletion list
//             if (!deletedUserIds.includes(userId)) {
//               updatedUserIds.push(userId);
//               continue;
//             }

//             // Get the timestamp for when this user's security_user_id was last modified
//             const userLastModifiedTimestamp = existingMsg.security_user_id_lastModifiedDateTime?.[userId];
            
//             // If no timestamp exists or the deletion is more recent than the last addition,
//             // remove the user. Otherwise, keep them (they were re-added after this deletion)
//             if (!userLastModifiedTimestamp || deletionTimestamp > userLastModifiedTimestamp) {
//               // User should be removed - don't add to updatedUserIds
//               logger.log(`[CosmosDB:Process] Removing user ${userId} from message ${existingMsg.id}. Deletion time: ${deletionTimestamp}, Last modified: ${userLastModifiedTimestamp || 'N/A'}`);
//             } else {
//               // User was re-added after this deletion - keep them
//               updatedUserIds.push(userId);
//               skippedUserIds.push(userId);
//               logger.log(`[CosmosDB:Process] Keeping user ${userId} in message ${existingMsg.id} as they were re-added after deletion. Deletion time: ${deletionTimestamp}, Last modified: ${userLastModifiedTimestamp}`);
//             }
//           }

//           // Only update if there's a change in the array
//           if (updatedUserIds.length < existingUserIds.length) {
//             // Record how many users were actually removed (not including skipped ones)
//             const actuallyRemoved = userIdsToRemove.length - skippedUserIds.length;
            
//             // Update the document with new security_user_id array
//             await container.item(existingMsg.id, existingMsg.id).replace({
//               ...existingMsg,
//               security_user_id: updatedUserIds,
//               // We don't need to update security_user_id_lastModifiedDateTime here as we're only removing
//             });
            
//             removedCount += actuallyRemoved;
            
//             logger.log(`[CosmosDB:Process] Message ${existingMsg.id}: Removed ${actuallyRemoved} user IDs, skipped ${skippedUserIds.length} re-added users`);
//             logger.log(`[CosmosDB:Process] Message ${existingMsg.id}: Updated security_user_id: ${JSON.stringify(updatedUserIds)}`);
//           } else if (skippedUserIds.length > 0) {
//             logger.log(`[CosmosDB:Process] All users were skipped (${skippedUserIds.length}) as they were re-added after deletion`);
//           } else {
//             logger.log(`[CosmosDB:Process] No user IDs removed from message: ${existingMsg.id}`);
//           }
//         }
//       } catch (error) {
//         logger.log(`[CosmosDB:Process] Error processing deleted users for chatId ${message.chatId}: ${error}`);
//       }
//     }
//   }
  
//   return removedCount;
// }

// export async function deleteAllDocumentsFromCosmosDB(
//   logger: CustomLogger
// ): Promise<void> {
//   try {
//     if (!databaseName || !containerName) {
//       throw new Error(
//         "[CosmosDB:Delete] databaseName and containerName must be defined"
//       );
//     }

//     const client = getClient(logger);
//     const database = client.database(databaseName);
//     const container = database.container(containerName);

//     logger.log(`[CosmosDB:Delete] Deleting Entire Container: ${containerName}`);

//     await container.delete();
//     await database.containers.createIfNotExists({ id: containerName });

//     logger.log(`[CosmosDB:Delete] All Data Deleted. Container Recreated.`);
//   } catch (error) {
//     logger.log(`[CosmosDB:Delete] Error: ${error}`);
//     throw error;
//   }
// }
